package com.xtgl.ssystem.service;

import com.xtgl.ssystem.common.dto.SingleDto;
import com.xtgl.ssystem.common.entity.Single;

import java.util.List;

/**
 * 单机服务接口
 */
public interface SingleService {
    
    /**
     * 添加单机
     * @param singleDto 单机信息
     * @return 单机ID
     */
    Long addSingle(SingleDto singleDto);
    
    /**
     * 查询某分系统下的所有单机
     * @param subsystemId 分系统ID
     * @return 单机列表
     */
    List<Single> getSinglesBySubsystemId(Long subsystemId);
    
    /**
     * 删除单机（级联删除其下所有模块）
     * @param singleId 单机ID
     */
    void deleteSingle(Long singleId);
    
    /**
     * 编辑单机
     * @param singleId 单机ID
     * @param singleDto 单机信息
     */
    void updateSingle(Long singleId, SingleDto singleDto);
    
    /**
     * 根据ID获取单机
     * @param singleId 单机ID
     * @return 单机信息
     */
    Single getSingleById(Long singleId);
    
    /**
     * 根据分系统ID删除所有单机
     * @param subsystemId 分系统ID
     */
    void deleteSinglesBySubsystemId(Long subsystemId);
}
