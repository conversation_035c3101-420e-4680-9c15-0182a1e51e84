package com.xtgl.ssystem.common.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 遥测代号绑定响应DTO
 */
@Data
public class TelemetryBindResponseDto {

    /**
     * 绑定对象的层级
     */
    private String level;

    /**
     * 对应层级的ID
     */
    private Long id;

    /**
     * 绑定的遥测代号
     */
    private String telemetryCode;

    /**
     * 绑定时间
     */
    private LocalDateTime boundAt;

    /**
     * 绑定的遥测代号ID
     */
    private Long telemetryCodeId;
}
