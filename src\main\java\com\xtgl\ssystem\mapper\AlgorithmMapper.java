package com.xtgl.ssystem.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xtgl.ssystem.common.entity.Algorithm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 算法Mapper接口
 */
@Mapper
public interface AlgorithmMapper extends BaseMapper<Algorithm> {

    /**
     * 分页搜索算法
     *
     * @param page 分页对象
     * @param name 算法名称关键字
     * @param type 算法类型
     * @return 分页结果
     */
    IPage<Algorithm> searchAlgorithms(Page<Algorithm> page, 
                                     @Param("name") String name, 
                                     @Param("type") Integer type);
}
