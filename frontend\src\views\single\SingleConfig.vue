<template>
  <div class="single-config-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>单机配置</h2>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-section">
      <div class="search-row">
        <!-- 航天器型号 -->
        <div class="search-item">
          <label>航天器型号：</label>
          <el-input
            v-model="searchForm.model"
            placeholder="请输入航天器型号"
            clearable
            class="search-input"
          >
            <template #suffix>
              <el-icon class="search-icon">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>

        <!-- 航天器名称 -->
        <div class="search-item">
          <label>航天器名称：</label>
          <el-input
            v-model="searchForm.name"
            placeholder="请输入航天器名称"
            clearable
            class="search-input"
          >
            <template #suffix>
              <el-icon class="search-icon">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>

        <!-- 负责人名称 -->
        <div class="search-item">
          <label>负责人名称：</label>
          <el-input
            v-model="searchForm.header"
            placeholder="请输入负责人名称"
            clearable
            class="search-input"
          >
            <template #suffix>
              <el-icon class="search-icon">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>

        <!-- 所属单位 -->
        <div class="search-item">
          <label>所属单位：</label>
          <el-input
            v-model="searchForm.company"
            placeholder="请输入所属单位"
            clearable
            class="search-input"
          >
            <template #suffix>
              <el-icon class="search-icon">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="search-buttons">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="model" label="型号" width="120" />
        <el-table-column prop="name" label="名称" width="180" />
        <el-table-column prop="header" label="负责人" width="120" />
        <el-table-column prop="createTime" label="创建时间" width="120" />
        <el-table-column prop="company" label="所属单位" width="200" />
        <el-table-column prop="receiveTime" label="开始接收时间" width="160" />
        <el-table-column prop="status" label="卫星状态" width="120" />
        <el-table-column label="遥测代号" width="120">
          <template #default="scope">
            <el-button
              type="text"
              @click="handleViewTelemetry(scope.row)"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button
              type="text"
              style="color: #409EFF"
              @click="handleConfig(scope.row)"
            >
              配置
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { craftSearch } from '@/api/single'

// 搜索表单
const searchForm = reactive({
  model: '',
  name: '',
  header: '',
  company: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 搜索航天器数据
const searchData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNo: pagination.page,
      pageSize: pagination.size
    }
    
    const response = await craftSearch(params)
    if (response.code === 200) {
      tableData.value = response.data.list || []
      pagination.total = response.data.total || 0
      pagination.page = response.data.page || 1
      pagination.size = response.data.size || 10
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 搜索按钮点击
const handleSearch = () => {
  pagination.page = 1
  searchData()
}

// 重置按钮点击
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.page = 1
  searchData()
}

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.size = val
  pagination.page = 1
  searchData()
}

// 当前页改变
const handleCurrentChange = (val) => {
  pagination.page = val
  searchData()
}

// 查看遥测代号
const handleViewTelemetry = (row) => {
  ElMessage.info('遥测代号查看功能暂未开发')
  // TODO: 跳转到遥测代号查看页面
}

// 配置按钮点击
const handleConfig = (row) => {
  ElMessage.info('配置功能暂未开发')
  // TODO: 跳转到配置页面
}

// 页面加载时获取数据
onMounted(() => {
  searchData()
})
</script>

<style scoped>
.single-config-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.search-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  align-items: center;
  min-width: 280px;
}

.search-item label {
  white-space: nowrap;
  margin-right: 8px;
  color: #333;
  font-weight: 500;
}

.search-input {
  width: 200px;
}

.search-icon {
  color: #999;
  cursor: pointer;
}

.search-buttons {
  display: flex;
  gap: 10px;
}

.table-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-section {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
