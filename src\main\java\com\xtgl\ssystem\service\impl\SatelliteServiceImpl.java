package com.xtgl.ssystem.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtgl.ssystem.common.dto.MqttDto;
import com.xtgl.ssystem.common.dto.MqttTestResultDto;
import com.xtgl.ssystem.common.dto.SatelliteDto;
import com.xtgl.ssystem.common.dto.SatelliteSearchDto;
import com.xtgl.ssystem.common.dto.TelemetryCodeDto;
import com.xtgl.ssystem.common.entity.Batch;
import com.xtgl.ssystem.common.entity.PageResult;
import com.xtgl.ssystem.common.entity.Satellite;
import com.xtgl.ssystem.common.entity.TelemetryCode;
import com.xtgl.ssystem.mapper.BatchMapper;
import com.xtgl.ssystem.mapper.SatelliteMapper;
import com.xtgl.ssystem.mapper.TelemetryCodeMapper;
import com.xtgl.ssystem.service.MqttService;
import com.xtgl.ssystem.service.SatelliteService;
import com.xtgl.ssystem.service.TelemetryBindService;
import com.xtgl.ssystem.util.ExcelUtil;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 航天器服务实现类
 */
@Service
public class SatelliteServiceImpl extends ServiceImpl<SatelliteMapper, Satellite> implements SatelliteService {

    @Autowired
    private SatelliteMapper satelliteMapper;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private TelemetryCodeMapper telemetryCodeMapper;

    @Autowired
    private MqttService mqttService;

    @Autowired
    private TelemetryBindService telemetryBindService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> createSatellite(SatelliteDto satelliteDto) {
        // 检查名称是否已存在
        checkNameExists(satelliteDto.getName(), null);
        
        // 检查MQTT名称是否存在
        checkMqttNameExists(satelliteDto.getMqttName());
        

        
        // 检查批次是否存在
        checkBatchExists(satelliteDto.getBatch());
        
        // DTO转换为实体
        Satellite satellite = new Satellite();
        BeanUtils.copyProperties(satelliteDto, satellite);
        
        // 设置创建时间
        LocalDate now = LocalDate.now();
        satellite.setCreateTime(now);
        
        // 设置初始状态
        satellite.setStatus("未开始");
        
        // 保存航天器
        satelliteMapper.insert(satellite);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("id", satellite.getId());
        result.put("createTime", LocalDateTime.now().format(DateTimeFormatter.ISO_DATE_TIME));
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteSatellites(List<Long> ids, boolean all) {
        if (all) {
            // 删除所有航天器（管理员权限）
            Long count = satelliteMapper.selectCount(null);

            // 先删除所有遥测数据
            telemetryCodeMapper.delete(null);

            // 再删除所有航天器
            satelliteMapper.delete(null);
            return count.intValue();
        } else {
            // 删除指定ID的航天器
            int deletedCount = 0;

            for (Long satelliteId : ids) {
                // 检查航天器是否存在
                Satellite satellite = satelliteMapper.selectById(satelliteId);
                if (satellite == null) {
                    continue; // 航天器不存在，跳过
                }

                try {
                    // 先删除该航天器的遥测数据
                    int telemetryDeleted = telemetryCodeMapper.deleteBySatelliteId(satelliteId);

                    // 再删除航天器
                    int result = satelliteMapper.deleteById(satelliteId);
                    if (result > 0) {
                        deletedCount++;
                        // 可以记录删除的详细信息
                        System.out.println("成功删除航天器 " + satellite.getName() + " (ID: " + satelliteId + ") 及其 " + telemetryDeleted + " 条遥测数据");
                    }
                } catch (Exception e) {
                    // 在事务中，如果有错误应该抛出异常以回滚
                    throw new RuntimeException("删除航天器 " + satelliteId + " 失败: " + e.getMessage(), e);
                }
            }

            return deletedCount;
        }
    }

    @Override
    public PageResult<SatelliteDto> searchSatellites(SatelliteSearchDto searchDto) {
        // 构建分页参数
        Page<Satellite> page = new Page<>(searchDto.getPageNo(), searchDto.getPageSize());
        
        // 分页查询
        IPage<Satellite> satellitePage = satelliteMapper.selectPageByCondition(
                page,
                searchDto.getModel(),
                searchDto.getName(),
                searchDto.getHeader(),
                searchDto.getCompany());
        
        // 转换为DTO列表
        List<SatelliteDto> satelliteDtoList = new ArrayList<>();
        for (Satellite satellite : satellitePage.getRecords()) {
            SatelliteDto dto = convertEntityToDto(satellite);
            satelliteDtoList.add(dto);
        }
        
        // 构建分页结果
        return PageResult.build(satellitePage.getTotal(), satelliteDtoList);
    }

    @Override
    public SatelliteDto getSatelliteById(Long id) {
        Satellite satellite = satelliteMapper.selectById(id);
        if (satellite == null) {
            return null;
        }
        
        return convertEntityToDto(satellite);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SatelliteDto updateSatellite(SatelliteDto satelliteDto) {
        // 检查ID是否存在
        Long id = satelliteDto.getId();
        if (id == null) {
            throw new IllegalArgumentException("ID不能为空");
        }
        
        // 检查航天器是否存在
        Satellite existSatellite = satelliteMapper.selectById(id);
        if (existSatellite == null) {
            throw new IllegalArgumentException("航天器不存在");
        }
        
        // 检查名称是否已存在（排除自身）
        checkNameExists(satelliteDto.getName(), id);
        
        // 检查MQTT名称是否存在
        checkMqttNameExists(satelliteDto.getMqttName());
        

        
        // 检查批次是否存在
        checkBatchExists(satelliteDto.getBatch());
        
        // 更新实体
        BeanUtils.copyProperties(satelliteDto, existSatellite);
        // 保留创建时间
        existSatellite.setCreateTime(existSatellite.getCreateTime());
        
        // 保存更新
        satelliteMapper.updateById(existSatellite);
        
        return convertEntityToDto(existSatellite);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addBatches(List<Integer> batches) {
        if (batches == null || batches.isEmpty()) {
            return 0;
        }
        
        // 过滤掉已存在的批次
        List<Batch> existingBatches = batchMapper.selectList(null);
        Set<Integer> existingBatchNames = existingBatches.stream()
                .map(Batch::getName)
                .collect(Collectors.toSet());
        
        List<Integer> newBatches = batches.stream()
                .distinct() // 去重
                .filter(batch -> !existingBatchNames.contains(batch))
                .collect(Collectors.toList());
        
        if (newBatches.isEmpty()) {
            return 0;
        }
        
        // 批量插入新批次
        return batchMapper.batchInsert(newBatches);
    }

    @Override
    public List<Integer> getAllBatches() {
        return batchMapper.selectAllBatchNames();
    }

    @Override
    public MqttTestResultDto testMqttConnection(String mqttName) {
        // 根据名称查询MQTT配置
        MqttDto mqttDto = mqttService.getMqttByName(mqttName);
        if (mqttDto == null) {
            throw new IllegalArgumentException("MQTT连接不存在");
        }
        
        // 测试连接
        return mqttService.testMqttConnection(mqttDto);
    }

    @Override
    public List<String> getAllMqttNames() {
        // 获取所有MQTT名称列表
        return mqttService.getAllMqttNames();
    }

    @Override
    public Map<String, Object> uploadTelemetryCodeFile(MultipartFile file) {
        try {
            // 解析Excel文件，传入null作为satelliteId，表示不绑定航天器
            Map<String, Object> parseResult = ExcelUtil.parseTelemetryCodeExcel(file, null);
            
            // 获取解析结果
            String errorMsg = (String) parseResult.get("errorMsg");
            if (errorMsg != null) {
                throw new IllegalArgumentException(errorMsg);
            }
            
            List<TelemetryCode> successList = (List<TelemetryCode>) parseResult.get("successList");
            List<Map<String, Object>> failList = (List<Map<String, Object>>) parseResult.get("failList");
            
            // 验证上传的数据量
            if (successList.isEmpty() && failList.isEmpty()) {
                throw new IllegalArgumentException("Excel文件中没有有效数据");
            }
            
            if (successList.size() + failList.size() > 5000) {
                throw new IllegalArgumentException("一次最多上传5000条数据");
            }
            
            // 不写入数据库，而是存入临时存储
            String uploadId = UUID.randomUUID().toString();
            ExcelUtil.saveToTempStorage(uploadId, successList);
            
            // 生成返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("uploadId", uploadId);
            result.put("total", successList.size() + failList.size());
            result.put("valid", successList.size());
            result.put("invalid", failList.size());
            
            // 如果有失败数据，生成失败数据Excel文件
            if (!failList.isEmpty()) {
                String failFileUrl = ExcelUtil.generateFailExcel(failList);
                result.put("failFileUrl", failFileUrl);
            }
            
            return result;
        } catch (IOException e) {
            throw new RuntimeException("解析Excel文件失败: " + e.getMessage(), e);
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTelemetryCode(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("遥测代号ID不能为空");
        }
        
        int result = telemetryCodeMapper.deleteById(id);
        return result > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteTelemetryCodes(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        
        return telemetryCodeMapper.deleteBatchIds(ids);
    }

    @Override
    public Map<String, Object> uploadTelemetryCodes(MultipartFile file, Long satelliteId) {
        // 检查航天器是否存在
        Satellite satellite = satelliteMapper.selectById(satelliteId);
        if (satellite == null) {
            throw new IllegalArgumentException("航天器不存在");
        }
        
        try {
            // 解析Excel文件
            Map<String, Object> parseResult = ExcelUtil.parseTelemetryCodeExcel(file, satelliteId);
            
            // 获取解析结果
            String errorMsg = (String) parseResult.get("errorMsg");
            if (errorMsg != null) {
                throw new IllegalArgumentException(errorMsg);
            }
            
            List<TelemetryCode> successList = (List<TelemetryCode>) parseResult.get("successList");
            List<Map<String, Object>> failList = (List<Map<String, Object>>) parseResult.get("failList");
            
            // 验证上传的数据量
            if (successList.isEmpty() && failList.isEmpty()) {
                throw new IllegalArgumentException("Excel文件中没有有效数据");
            }
            
            if (successList.size() + failList.size() > 5000) {
                throw new IllegalArgumentException("一次最多上传5000条数据");
            }
            
            // 批量插入成功的数据
            if (!successList.isEmpty()) {
                try {
                    telemetryCodeMapper.batchInsert(successList);
                } catch (Exception e) {
                    // 数据库操作异常处理
                    throw new RuntimeException("保存遥测代号数据失败: " + e.getMessage(), e);
                }
            }
            
            // 生成返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", successList.size());
            result.put("failed", failList.size());
            
            // 如果有失败数据，生成失败数据Excel文件
            if (!failList.isEmpty()) {
                String failFileUrl = ExcelUtil.generateFailExcel(failList);
                result.put("failFileUrl", failFileUrl);
            }
            
            // 生成上传ID
            String uploadId = UUID.randomUUID().toString();
            result.put("uploadId", uploadId);
            
            return result;
        } catch (IOException e) {
            throw new RuntimeException("解析Excel文件失败: " + e.getMessage(), e);
        }
    }



    @Override
    public List<TelemetryCode> getTelemetryCodes(Long id, Long satelliteId) {
        if (id != null) {
            // 根据ID查询单条遥测代号
            TelemetryCode telemetryCode = telemetryCodeMapper.selectById(id);
            return telemetryCode != null ? Collections.singletonList(telemetryCode) : Collections.emptyList();
        } else if (satelliteId != null) {
            // 根据航天器ID查询所有遥测代号，使用去重逻辑
            return telemetryBindService.getBoundTelemetryCodes("satellite", satelliteId);
        }

        return Collections.emptyList();
    }

    @Override
    public List<TelemetryCodeDto> getTelemetryCodesDto(Long id, Long satelliteId) {
        // 先获取实体列表，再转换为DTO列表
        List<TelemetryCode> telemetryCodes = getTelemetryCodes(id, satelliteId);
        return convertTelemetryCodesToDtos(telemetryCodes);
    }

    /**
     * 将遥测代号实体转换为DTO
     * 
     * @param telemetryCode 遥测代号实体
     * @return 遥测代号DTO
     */
    private TelemetryCodeDto convertTelemetryCodeToDto(TelemetryCode telemetryCode) {
        if (telemetryCode == null) {
            return null;
        }
        
        TelemetryCodeDto dto = new TelemetryCodeDto();
        BeanUtils.copyProperties(telemetryCode, dto);
        
        // 可以在这里进行额外的字段处理，比如从description中提取code和unit
        String description = telemetryCode.getDescription();
        if (description != null && description.contains(":")) {
            String[] parts = description.split(":", 2);
            dto.setCode(parts[0].trim());
            if (parts.length > 1 && parts[1].contains("(") && parts[1].contains(")")) {
                int startIndex = parts[1].lastIndexOf('(');
                int endIndex = parts[1].lastIndexOf(')');
                if (startIndex >= 0 && endIndex > startIndex) {
                    dto.setUnit(parts[1].substring(startIndex + 1, endIndex).trim());
                }
            }
        }
        
        return dto;
    }

    /**
     * 将遥测代号实体列表转换为DTO列表
     * 
     * @param telemetryCodes 遥测代号实体列表
     * @return 遥测代号DTO列表
     */
    private List<TelemetryCodeDto> convertTelemetryCodesToDtos(List<TelemetryCode> telemetryCodes) {
        if (telemetryCodes == null || telemetryCodes.isEmpty()) {
            return Collections.emptyList();
        }
        
        return telemetryCodes.stream()
                .map(this::convertTelemetryCodeToDto)
                .collect(Collectors.toList());
    }




    


    /**
     * 检查航天器名称是否已存在
     *
     * @param name 名称
     * @param id   ID（更新时排除自身）
     */
    private void checkNameExists(String name, Long id) {
        LambdaQueryWrapper<Satellite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Satellite::getName, name);
        if (id != null) {
            queryWrapper.ne(Satellite::getId, id);
        }
        Long count = satelliteMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new IllegalArgumentException("航天器名称已存在");
        }
    }

    /**
     * 检查MQTT名称是否存在
     *
     * @param mqttName MQTT名称
     */
    private void checkMqttNameExists(String mqttName) {
        MqttDto mqttDto = mqttService.getMqttByName(mqttName);
        if (mqttDto == null) {
            throw new IllegalArgumentException("MQTT连接不存在");
        }
    }



    /**
     * 检查批次是否存在
     *
     * @param batch 批次
     */
    private void checkBatchExists(Integer batch) {
        LambdaQueryWrapper<Batch> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Batch::getName, batch);
        Long count = batchMapper.selectCount(queryWrapper);
        if (count == 0) {
            throw new IllegalArgumentException("批次不存在");
        }
    }

    /**
     * 实体转DTO
     *
     * @param satellite 航天器实体
     * @return 航天器DTO
     */
    private SatelliteDto convertEntityToDto(Satellite satellite) {
        SatelliteDto dto = new SatelliteDto();
        BeanUtils.copyProperties(satellite, dto);
        
        // 可以在这里添加其他字段的处理
        
        return dto;
    }



    @Override
    public Map<String, Object> uploadTelemetryCodeFileWithBatch(MultipartFile file, String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名（批次标识）不能为空");
        }

        try {
            // 解析Excel文件，不绑定航天器ID
            Map<String, Object> parseResult = ExcelUtil.parseTelemetryCodeExcel(file, null);

            List<TelemetryCode> successList = (List<TelemetryCode>) parseResult.get("successList");
            List<Map<String, Object>> failList = (List<Map<String, Object>>) parseResult.get("failList");
            String errorMsg = (String) parseResult.get("errorMsg");

            // 如果有解析错误，抛出异常
            if (errorMsg != null) {
                throw new IllegalArgumentException(errorMsg);
            }

            // 为成功解析的数据设置文件名作为批次标识
            for (TelemetryCode code : successList) {
                code.setFileName(fileName);
                code.setSpacecraftId(null); // 确保航天器ID为空（表示未绑定）
            }

            // 批量插入成功的数据
            if (!successList.isEmpty()) {
                try {
                    telemetryCodeMapper.batchInsert(successList);
                } catch (Exception e) {
                    throw new RuntimeException("保存遥测代号数据失败: " + e.getMessage(), e);
                }
            }

            // 查询保存后的数据
            List<TelemetryCode> savedCodes = telemetryCodeMapper.selectByFileName(fileName);

            // 生成返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", successList.size());
            result.put("failed", failList.size());
            result.put("fileName", fileName);
            result.put("data", savedCodes);

            // 如果有失败数据，生成失败数据Excel文件
            if (!failList.isEmpty()) {
                String failFileUrl = ExcelUtil.generateFailExcel(failList);
                result.put("failFileUrl", failFileUrl);
            }

            return result;
        } catch (IOException e) {
            throw new RuntimeException("解析Excel文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<TelemetryCodeDto> getTelemetryCodesByFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        // 根据文件名查询未绑定航天器的遥测代号
        List<TelemetryCode> telemetryCodes = telemetryCodeMapper.selectByFileName(fileName);
        return convertTelemetryCodesToDtos(telemetryCodes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> deleteTelemetryCodesByFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        // 删除指定文件名的临时遥测代号数据
        int deleted = telemetryCodeMapper.deleteByFileName(fileName);

        Map<String, Object> result = new HashMap<>();
        result.put("deleted", deleted);
        result.put("message", "成功删除 " + deleted + " 条临时数据");

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> updateSatelliteIdByFileName(Long satelliteId, String fileName) {
        if (satelliteId == null) {
            throw new IllegalArgumentException("航天器ID不能为空");
        }

        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        // 检查航天器是否存在
        Satellite satellite = satelliteMapper.selectById(satelliteId);
        if (satellite == null) {
            throw new IllegalArgumentException("航天器不存在");
        }

        // 更新文件名对应的遥测代号的航天器ID
        int updated = telemetryCodeMapper.updateSatelliteIdByFileName(satelliteId, fileName);

        Map<String, Object> result = new HashMap<>();
        result.put("updated", updated);
        result.put("satelliteId", satelliteId);
        result.put("satelliteName", satellite.getName());
        result.put("message", "成功关联 " + updated + " 条遥测代号到航天器: " + satellite.getName());

        return result;
    }

    @Override
    public Map<String, Object> getBoundFileInfo(Long satelliteId) {
        if (satelliteId == null) {
            throw new IllegalArgumentException("航天器ID不能为空");
        }

        // 检查航天器是否存在
        Satellite satellite = satelliteMapper.selectById(satelliteId);
        if (satellite == null) {
            throw new IllegalArgumentException("航天器不存在");
        }

        // 查询该航天器的遥测代号数据
        List<TelemetryCode> telemetryCodes = telemetryCodeMapper.selectBySatelliteId(satelliteId);

        Map<String, Object> result = new HashMap<>();

        if (telemetryCodes.isEmpty()) {
            // 没有绑定任何文件
            result.put("hasBoundFile", false);
            result.put("message", "未绑定任何文件");
            return result;
        }

        // 统计文件信息
        Map<String, Integer> fileStats = new HashMap<>();
        String boundFileName = null;

        for (TelemetryCode code : telemetryCodes) {
            String fileName = code.getFileName();
            if (fileName != null && !fileName.trim().isEmpty()) {
                fileStats.put(fileName, fileStats.getOrDefault(fileName, 0) + 1);
                if (boundFileName == null) {
                    boundFileName = fileName;
                }
            }
        }

        result.put("hasBoundFile", true);
        result.put("totalRecords", telemetryCodes.size());

        if (boundFileName != null) {
            // 有文件名信息
            result.put("fileName", boundFileName);
            result.put("fileStats", fileStats);
            result.put("message", "已绑定文件: " + boundFileName);
        } else {
            // 没有文件名信息（可能是旧数据）
            result.put("fileName", "未知文件");
            result.put("message", "已绑定数据，但文件名信息缺失");
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> deleteBoundTelemetryData(Long satelliteId) {
        if (satelliteId == null) {
            throw new IllegalArgumentException("航天器ID不能为空");
        }

        // 检查航天器是否存在
        Satellite satellite = satelliteMapper.selectById(satelliteId);
        if (satellite == null) {
            throw new IllegalArgumentException("航天器不存在");
        }

        // 删除该航天器的所有遥测数据
        int deleted = telemetryCodeMapper.deleteBySatelliteId(satelliteId);

        Map<String, Object> result = new HashMap<>();
        result.put("deleted", deleted);
        result.put("satelliteId", satelliteId);
        result.put("satelliteName", satellite.getName());
        result.put("message", "成功删除航天器 " + satellite.getName() + " 的 " + deleted + " 条遥测数据");

        return result;
    }
}