<template>
  <div class="mqtt-container">
    <h2 class="page-title">MQTT管理</h2>
    
    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-button type="primary" @click="openDialog('create')">新建连接</el-button>
    </div>
    
    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      stripe
      style="width: 100%"
    >
      <el-table-column prop="name" label="名称" min-width="120" />
      <el-table-column prop="ip" label="IP" min-width="120" />
      <el-table-column prop="port" label="端口" min-width="80" />
      <el-table-column prop="topic" label="主题" min-width="120" />
      <el-table-column prop="userName" label="用户名" min-width="120" />
      <el-table-column prop="note" label="备注" min-width="150" show-overflow-tooltip />
      <el-table-column label="启用状态" min-width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status ? 'success' : 'info'">
            {{ scope.row.status ? '已启用' : '未启用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="updatePerson" label="更新人" min-width="100" />
      <el-table-column label="更新时间" min-width="160">
        <template #default="scope">
          {{ scope.row.updateTime ? new Date(scope.row.updateTime).toLocaleString() : '' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openDialog('edit', scope.row)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 - 仅在有必要时显示 -->
    <div v-if="checkPaginationNeeded" class="pagination-container">
      <el-pagination
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 15, 20]"
        :total="computedTotal"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <div v-if="computedTotal > 0" class="pagination-info">
        共 {{ computedTotal }} 条记录，当前第 {{ queryParams.pageNum }} 页，每页 {{ queryParams.pageSize }} 条
        (显示 {{ ((queryParams.pageNum - 1) * queryParams.pageSize) + 1 }} - {{ Math.min(queryParams.pageNum * queryParams.pageSize, computedTotal) }} 条)
      </div>
      <div v-else class="pagination-info">
        暂无记录
      </div>
    </div>
    
    <!-- MQTT表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'create' ? '新建' : '编辑'"
      width="580px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入连接名称" />
        </el-form-item>
        <el-form-item label="主题" prop="topic">
          <el-input v-model="form.topic" placeholder="请输入主题" />
        </el-form-item>
        <el-form-item label="IP" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="form.port" :min="1" :max="65535" />
        </el-form-item>
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleTestConnection">测试连接</el-button>
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
          <el-switch v-model="form.status" active-color="#13ce66" inactive-color="#ff4949" />
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input v-model="form.note" type="textarea" :rows="2" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">提交</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="提示"
      width="360px"
    >
      <span>你确定删除此连接？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmDelete">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 连接测试结果对话框 -->
    <el-dialog
      v-model="testResultDialogVisible"
      title="连接测试结果"
      width="400px"
    >
      <div v-if="testResult">
        <div v-if="testResult.connected" class="test-result success">
          <el-icon class="test-icon"><check /></el-icon>
          <div class="test-info">
            <p>连接成功</p>
            <p>耗时: {{ testResult.timeCostMs }}ms</p>
            <p>MQTT版本: {{ testResult.mqttVersion }}</p>
          </div>
        </div>
        <div v-else class="test-result error">
          <el-icon class="test-icon"><close /></el-icon>
          <div class="test-info">
            <p>连接失败</p>
            <p>错误信息: {{ testResult.errorMsg }}</p>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="testResultDialogVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getMqttList,
  getMqttDetail,
  createMqtt,
  updateMqtt,
  deleteMqtt,
  testMqttConnection
} from '@/api/mqtt'

// 表格数据
const loading = ref(false)
const tableData = ref([]) // 当前页显示的数据
const allData = ref([])   // 所有数据

// 对话框控制
const dialogVisible = ref(false)
const dialogType = ref('create') // create or edit
const deleteDialogVisible = ref(false)
const deleteId = ref(null)
const testResultDialogVisible = ref(false)
const testResult = ref(null)

// 计算实际总条数
const computedTotal = computed(() => {
  return allData.value.length
})

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  status: null
})

// 表单数据和验证规则
const formRef = ref()
const form = reactive({
  id: null,
  name: '',
  topic: '',
  ip: '',
  port: 1883,
  userName: '',  // 使用userName而不是username，匹配后端
  password: '',
  status: true,
  note: '',
  updatePerson: 'admin' // 添加默认更新人
})
const rules = {
  name: [{ required: true, message: '请输入连接名称', trigger: 'blur' }],
  topic: [{ required: true, message: '请输入主题', trigger: 'blur' }],
  ip: [{ required: true, message: '请输入IP地址', trigger: 'blur' }],
  port: [{ required: true, message: '请输入端口号', trigger: 'blur' }],
  userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

// 初始化
onMounted(() => {
  loadMqttList()
})

// 加载MQTT列表数据
const loadMqttList = async () => {
  loading.value = true
  try {
    console.log('请求参数:', JSON.stringify(queryParams))
    // 在实际请求中，我们请求所有数据（不分页）
    const res = await getMqttList({
      pageNum: 1,  // 固定请求第一页
      pageSize: 100, // 请求足够大的数量以获取所有数据
      status: queryParams.status
    })
    
    // 解析数据
    if (res && res.code === 200 && res.data && Array.isArray(res.data.list)) {
      // 保存全部数据
      allData.value = res.data.list
      console.log(`成功获取全部数据 ${allData.value.length} 条`)
      
      // 更新当前页数据
      updatePageData()
    } else {
      console.error('返回数据异常', res)
      allData.value = []
      tableData.value = []
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
    allData.value = []
    tableData.value = []
  } finally {
    loading.value = false
  }
}

// 更新当前页数据
const updatePageData = () => {
  const startIndex = (queryParams.pageNum - 1) * queryParams.pageSize
  const endIndex = startIndex + queryParams.pageSize
  tableData.value = allData.value.slice(startIndex, endIndex)
  console.log(`当前页 ${queryParams.pageNum}, 显示 ${tableData.value.length} 条数据，总数 ${allData.value.length}`)
}

// 分页操作
const handleSizeChange = (size) => {
  console.log('页大小变更:', size)
  queryParams.pageSize = size
  queryParams.pageNum = 1 // 切换页大小时重置为第一页
  updatePageData()
}

const handleCurrentChange = (page) => {
  console.log('页码变更:', page)
  queryParams.pageNum = page
  updatePageData()
}

// 打开对话框
const openDialog = async (type, row) => {
  dialogType.value = type
  resetForm()
  
  if (type === 'edit' && row) {
    try {
      const res = await getMqttDetail(row.id)
      Object.assign(form, res.data)
    } catch (error) {
      console.error('获取详情失败', error)
      ElMessage.error('获取详情失败')
    }
  }
  
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  form.id = null
  form.name = ''
  form.topic = ''
  form.ip = ''
  form.port = 1883
  form.userName = ''
  form.password = ''
  form.status = true
  form.note = ''
  form.updatePerson = 'admin'
}

// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid) => {
    if (!valid) return
    
    try {
      if (dialogType.value === 'create') {
        await createMqtt(form)
        ElMessage.success('创建成功')
      } else {
        await updateMqtt(form)
        ElMessage.success('更新成功')
      }
      
      dialogVisible.value = false
      loadMqttList() // 重新加载全部数据
    } catch (error) {
      console.error('操作失败', error)
      ElMessage.error(`${dialogType.value === 'create' ? '创建' : '更新'}失败`)
    }
  })
}

// 测试连接
const handleTestConnection = async () => {
  formRef.value.validate(async (valid) => {
    if (!valid) return
    
    try {
      const res = await testMqttConnection(form)
      testResult.value = res.data
      testResultDialogVisible.value = true
    } catch (error) {
      console.error('测试连接失败', error)
      ElMessage.error('测试连接失败')
    }
  })
}

// 删除操作
const handleDelete = (id) => {
  deleteId.value = id
  deleteDialogVisible.value = true
}

const confirmDelete = async () => {
  try {
    await deleteMqtt(deleteId.value)
    ElMessage.success('删除成功')
    deleteDialogVisible.value = false
    loadMqttList() // 重新加载全部数据
  } catch (error) {
    console.error('删除失败', error)
    ElMessage.error('删除失败')
  }
}

// 检查是否需要分页
const checkPaginationNeeded = computed(() => {
  return allData.value.length > queryParams.pageSize
})
</script>

<style scoped>
.mqtt-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.operation-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}

.pagination-container {
  margin-top: 20px;
  padding: 10px;
  text-align: right;
  background-color: #f0f9ff;
  border-radius: 4px;
}

.pagination-info {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
}

.test-result {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border-radius: 4px;
}

.test-result.success {
  background-color: #f0f9eb;
}

.test-result.error {
  background-color: #fef0f0;
}

.test-icon {
  font-size: 24px;
  margin-right: 15px;
}

.test-result.success .test-icon {
  color: #67c23a;
}

.test-result.error .test-icon {
  color: #f56c6c;
}

.test-info p {
  margin: 5px 0;
}
</style> 