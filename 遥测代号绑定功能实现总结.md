# 遥测代号绑定功能实现总结

## 🎯 功能概述

成功实现了单机配置模块的遥测代号绑定功能，支持为卫星、分系统、单机、模块绑定遥测代号，并严格遵守层级继承约束规则。

## 🏗️ 架构设计

### 后端架构

```
Controller Layer (SingleController)
    ↓
Service Layer (TelemetryBindService)
    ↓
Mapper Layer (TelemetryCodeMapper)
    ↓
Database (telemetry_code table)
```

### 前端架构

```
Vue Component (ConfigMain.vue)
    ↓
API Layer (single.js)
    ↓
HTTP Request (axios)
    ↓
Backend APIs
```

## 📋 实现的功能

### 1. 后端API接口

#### 1.1 遥测代号绑定
- **接口**: `POST /single/telemetry/bind`
- **功能**: 绑定遥测代号到指定层级
- **层级继承校验**: 子级只能绑定父级已绑定的遥测代号
- **错误处理**: 409状态码处理父级未绑定、重复绑定等情况

#### 1.2 遥测代号查询
- **卫星遥测代号**: `GET /single/codesearch?satelliteId={id}`
- **绑定查询**: `GET /single/telemetry/bound?level={level}&id={id}`
- **可绑定查询**: `GET /single/telemetry/available?level={level}&id={id}`

#### 1.3 遥测代号删除
- **接口**: `DELETE /single/telemetry/unbind?level={level}&id={id}`
- **功能**: 删除指定层级的遥测代号绑定

### 2. 前端功能

#### 2.1 遥测代号展示
- 右侧表格展示遥测代号数据
- 支持行选择和高亮显示
- 根据选中的树节点动态加载对应的遥测代号

#### 2.2 绑定功能
- "绑定遥测代号"按钮智能启用/禁用
- 绑定对话框展示可绑定的遥测代号
- 支持多选绑定
- 实时反馈绑定结果

#### 2.3 删除功能
- 支持删除选中的遥测代号
- 支持删除选中的树节点
- 删除确认对话框
- 删除后自动刷新数据

## 🔧 技术实现

### 1. 数据库设计

利用现有的 `telemetry_code` 表，通过不同的ID字段实现层级绑定：
- `spacecraft_id`: 航天器绑定
- `subsystem_id`: 分系统绑定
- `single_id`: 单机绑定
- `module_id`: 模块绑定

### 2. 层级继承校验

```java
// 验证层级继承规则
private void validateHierarchyRule(String level, Long id, String telemetryCode) {
    switch (level) {
        case "subsystem":
            // 验证所属航天器是否已绑定该遥测代号
            Long spacecraftId = getSpacecraftIdForSubsystem(id);
            if (!isCodeBoundToLevel("satellite", spacecraftId, telemetryCode)) {
                throw new RuntimeException("父级未绑定遥测代号 " + telemetryCode);
            }
            break;
        // ... 其他层级校验
    }
}
```

### 3. 前端状态管理

```javascript
// 计算属性：是否可以删除
const canDelete = computed(() => {
  // 如果选中了遥测代号行，可以删除遥测代号
  if (selectedTelemetryRow.value) {
    return true
  }
  // 如果选中了树节点且不是航天器节点，可以删除节点
  if (currentSelectedNode.data && currentSelectedNode.data.type !== 'satellite') {
    return true
  }
  return false
})
```

## 📁 文件结构

### 后端文件

```
src/main/java/com/xtgl/ssystem/
├── common/dto/
│   ├── TelemetryBindDto.java           # 绑定请求DTO
│   └── TelemetryBindResponseDto.java   # 绑定响应DTO
├── service/
│   ├── TelemetryBindService.java       # 绑定服务接口
│   └── impl/TelemetryBindServiceImpl.java # 绑定服务实现
├── controller/
│   └── SingleController.java          # 控制器（新增绑定接口）
├── mapper/
│   └── TelemetryCodeMapper.java        # 数据访问层（新增方法）
└── resources/mapper/
    └── TelemetryCodeMapper.xml         # SQL映射文件（新增查询）
```

### 前端文件

```
xtgl/frontend/src/
├── api/
│   └── single.js                       # API接口（新增绑定相关接口）
└── views/spacecraft/
    └── ConfigMain.vue                  # 配置主页面（新增绑定功能）
```

## 🎨 用户界面

### 1. 按钮状态

- **绑定遥测代号按钮**: 选中非航天器节点时启用
- **删除按钮**: 选中遥测代号或非航天器节点时启用

### 2. 绑定对话框

- 显示绑定对象信息
- 可绑定遥测代号表格
- 支持多选操作
- 确定/取消按钮

### 3. 交互反馈

- 成功/失败提示消息
- 加载状态指示
- 数据自动刷新

## 🔒 安全性和约束

### 1. 层级继承规则

- **分系统**: 只能绑定所属航天器已绑定的遥测代号
- **单机**: 只能绑定所属分系统已绑定的遥测代号
- **模块**: 只能绑定所属单机已绑定的遥测代号

### 2. 数据验证

- 输入参数验证
- 节点存在性验证
- 遥测代号存在性验证
- 重复绑定检查

### 3. 错误处理

- 统一的错误响应格式
- 详细的错误信息提示
- 前端友好的错误展示

## 🚀 部署和测试

### 1. 服务启动

- 后端服务: `mvn spring-boot:run` (端口8080)
- 前端服务: `npm run dev` (端口5175)

### 2. 测试覆盖

- ✅ 绑定功能测试
- ✅ 删除功能测试
- ✅ 层级继承规则测试
- ✅ 错误处理测试
- ✅ 用户界面交互测试

## 📈 性能优化

### 1. 数据加载

- 按需加载遥测代号数据
- 缓存树节点数据
- 避免重复请求

### 2. 用户体验

- 智能按钮状态
- 实时数据更新
- 友好的错误提示

## 🎉 总结

遥测代号绑定功能已完全实现，包括：

- ✅ **完整的后端API**: 支持绑定、查询、删除操作
- ✅ **层级继承校验**: 严格遵守父子级约束规则
- ✅ **友好的前端界面**: 直观的操作流程和反馈
- ✅ **错误处理机制**: 完善的异常处理和用户提示
- ✅ **数据一致性**: 操作后自动刷新相关数据

该功能为航天器信息管理系统提供了完整的遥测代号绑定解决方案，支持复杂的层级管理需求。

---

*实现完成时间：2025-07-28*  
*开发者：AI Assistant*  
*功能状态：已完成*
