# MQTT管理模块

## 项目概述

本模块为MQTT管理模块，提供MQTT连接配置的增删改查功能，以及连接测试功能。

## 技术栈

- 后端：Java 17、Maven 3.9.11、Spring Boot 3.1.9、MyBatis Plus *******
- 数据库：MySQL 8.0
- MQTT客户端：Eclipse Paho 1.2.5

## 功能列表

1. **新建连接**
   - 端点：POST /api/mqtt/new
   - 功能：注册新的MQTT连接配置

2. **测试连接**
   - 端点：POST /api/mqtt/test
   - 功能：测试MQTT连接可用性，返回连接状态、耗时和版本信息

3. **编辑连接**
   - 端点：PUT /api/mqtt/update
   - 功能：更新已有MQTT连接配置

4. **删除连接**
   - 端点：DELETE /api/mqtt/delete?id=xxx
   - 功能：删除指定MQTT连接配置

5. **查询连接**
   - 详情查询：GET /api/mqtt/{id}
   - 列表查询：GET /api/mqtt?pageNum=1&pageSize=10&status=true
   - 功能：查询MQTT连接配置详情或列表

## 项目结构

```
mqtt-management/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── xtgl/
│   │   │           └── mqtt/
│   │   │               ├── common/
│   │   │               │   ├── util/
│   │   │               │   │   ├── IpUtil.java
│   │   │               │   │   └── MqttClientUtil.java
│   │   │               │   ├── GlobalExceptionHandler.java
│   │   │               │   ├── PageResult.java
│   │   │               │   └── Result.java
│   │   │               ├── controller/
│   │   │               │   └── MqttController.java
│   │   │               ├── dto/
│   │   │               │   ├── MqttDto.java
│   │   │               │   └── MqttTestResultDto.java
│   │   │               ├── entity/
│   │   │               │   └── MqttConfig.java
│   │   │               ├── mapper/
│   │   │               │   └── MqttConfigMapper.java
│   │   │               ├── service/
│   │   │               │   ├── impl/
│   │   │               │   │   └── MqttServiceImpl.java
│   │   │               │   └── MqttService.java
│   │   │               └── MqttApplication.java
│   │   └── resources/
│   │       ├── mapper/
│   │       │   └── MqttConfigMapper.xml
│   │       └── application.yml
│   └── test/
├── mqtt.sql
├── pom.xml
└── README.md
```

## 环境配置

### 数据库配置

1. 执行 `mqtt.sql` 创建所需的数据库和表结构
2. 在 `application.yml` 中修改数据库连接信息

### 启动应用

1. 编译项目：`mvn clean package`
2. 运行应用：`java -jar mqtt-management-1.0.0.jar`

## API文档

### 1. 新建连接

**请求**

```http
POST /api/mqtt/new
Content-Type: application/json

{
  "name": "测试连接",
  "ip": "*************",
  "port": 1883,
  "topic": "test/topic",
  "userName": "admin",
  "password": "password",
  "status": true,
  "note": "测试用MQTT连接"
}
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "测试连接",
    "ip": "*************",
    "port": 1883,
    "topic": "test/topic",
    "userName": "admin",
    "password": "password",
    "status": true,
    "note": "测试用MQTT连接",
    "updatePerson": null,
    "updateTime": null
  }
}
```

### 2. 测试连接

**请求**

```http
POST /api/mqtt/test
Content-Type: application/json

{
  "name": "测试连接",
  "ip": "*************",
  "port": 1883,
  "topic": "test/topic",
  "userName": "admin",
  "password": "password"
}
```

**成功响应**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "connected": true,
    "timeCostMs": 123,
    "mqttVersion": "3.1.1",
    "errorMsg": null
  }
}
```

**失败响应**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "connected": false,
    "timeCostMs": 1000,
    "mqttVersion": null,
    "errorMsg": "连接失败: Connection refused"
  }
}
```

### 3. 编辑连接

**请求**

```http
PUT /api/mqtt/update
Content-Type: application/json

{
  "id": 1,
  "name": "已更新的连接",
  "ip": "*************",
  "port": 1883,
  "topic": "updated/topic",
  "userName": "admin",
  "password": "newpassword",
  "status": true,
  "note": "已更新的描述",
  "updatePerson": "admin"
}
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "已更新的连接",
    "ip": "*************",
    "port": 1883,
    "topic": "updated/topic",
    "userName": "admin",
    "password": "newpassword",
    "status": true,
    "note": "已更新的描述",
    "updatePerson": "admin",
    "updateTime": "2023-07-22 10:11:12"
  }
}
```

### 4. 删除连接

**请求**

```http
DELETE /api/mqtt/delete?id=1
```

**响应**

```json
{
  "code": 200,
  "message": "删除成功"
}
```

### 5. 查询连接

#### 详情查询

**请求**

```http
GET /api/mqtt/1
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "测试连接",
    "ip": "*************",
    "port": 1883,
    "topic": "test/topic",
    "userName": "admin",
    "password": "password",
    "status": true,
    "note": "测试用MQTT连接",
    "updatePerson": "admin",
    "updateTime": "2023-07-22 10:11:12"
  }
}
```

#### 列表查询

**请求**

```http
GET /api/mqtt?pageNum=1&pageSize=10&status=true
```

**响应**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 2,
    "list": [
      {
        "id": 1,
        "name": "生产环境Broker",
        "ip": "***********",
        "port": 8883,
        "topic": "实验",
        "userName": "张三",
        "note": "生产...",
        "status": true,
        "updatePerson": "张三",
        "updateTime": "2024-07-22 10:11:12"
      },
      {
        "id": 2,
        "name": "测试环境Broker",
        "ip": "***********",
        "port": 1883,
        "topic": "测试",
        "userName": "李四",
        "note": "测试...",
        "status": true,
        "updatePerson": "李四",
        "updateTime": "2024-07-23 10:11:12"
      }
    ]
  }
}
``` 