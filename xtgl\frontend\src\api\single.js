import request from './request'

/**
 * 航天器信息搜索
 * @param {Object} params 搜索参数
 * @param {string} params.model 型号，可精确匹配
 * @param {string} params.name 名称，模糊匹配
 * @param {string} params.header 负责人，模糊匹配
 * @param {string} params.company 研制单位，模糊匹配
 * @param {number} params.pageNo 页码，默认1
 * @param {number} params.pageSize 每页条数，默认10
 * @returns {Promise} 返回搜索结果
 */
export function craftSearch(params) {
  return request({
    url: '/single/craftsearch',
    method: 'get',
    params
  })
}

// ==================== 分系统管理 ====================

/**
 * 添加分系统
 * @param {Object} data 分系统数据
 * @returns {Promise} 返回添加结果
 */
export function addSubsystem(data) {
  return request({
    url: '/single/subsystemadd',
    method: 'post',
    data
  })
}

/**
 * 查询某卫星下的所有分系统
 * @param {number} satelliteId 航天器ID
 * @returns {Promise} 返回分系统列表
 */
export function getSubsystemsBySatelliteId(satelliteId) {
  return request({
    url: `/single/satellite/${satelliteId}/subsystems`,
    method: 'get'
  })
}

/**
 * 删除分系统
 * @param {number} subsystemId 分系统ID
 * @returns {Promise} 返回删除结果
 */
export function deleteSubsystem(subsystemId) {
  return request({
    url: `/single/subsystems/${subsystemId}`,
    method: 'delete'
  })
}

/**
 * 编辑分系统
 * @param {number} subsystemId 分系统ID
 * @param {Object} data 分系统数据
 * @returns {Promise} 返回编辑结果
 */
export function updateSubsystem(subsystemId, data) {
  return request({
    url: `/single/subsystems/${subsystemId}`,
    method: 'put',
    data
  })
}

// ==================== 单机管理 ====================

/**
 * 添加单机
 * @param {Object} data 单机数据
 * @returns {Promise} 返回添加结果
 */
export function addSingle(data) {
  return request({
    url: '/single/singleadd',
    method: 'post',
    data
  })
}

/**
 * 查询某分系统下的所有单机
 * @param {number} subsystemId 分系统ID
 * @returns {Promise} 返回单机列表
 */
export function getSinglesBySubsystemId(subsystemId) {
  return request({
    url: `/single/subsystem/${subsystemId}/singles`,
    method: 'get'
  })
}

/**
 * 删除单机
 * @param {number} singleId 单机ID
 * @returns {Promise} 返回删除结果
 */
export function deleteSingle(singleId) {
  return request({
    url: `/single/singles/${singleId}`,
    method: 'delete'
  })
}

/**
 * 编辑单机
 * @param {number} singleId 单机ID
 * @param {Object} data 单机数据
 * @returns {Promise} 返回编辑结果
 */
export function updateSingle(singleId, data) {
  return request({
    url: `/single/singles/${singleId}`,
    method: 'put',
    data
  })
}

// ==================== 模块管理 ====================

/**
 * 添加模块
 * @param {Object} data 模块数据
 * @returns {Promise} 返回添加结果
 */
export function addModule(data) {
  return request({
    url: '/single/moduleadd',
    method: 'post',
    data
  })
}

/**
 * 查询某单机下的所有模块
 * @param {number} singleId 单机ID
 * @returns {Promise} 返回模块列表
 */
export function getModulesBySingleId(singleId) {
  return request({
    url: `/single/single/${singleId}/modules`,
    method: 'get'
  })
}

/**
 * 删除模块
 * @param {number} moduleId 模块ID
 * @returns {Promise} 返回删除结果
 */
export function deleteModule(moduleId) {
  return request({
    url: `/single/modules/${moduleId}`,
    method: 'delete'
  })
}

/**
 * 编辑模块
 * @param {number} moduleId 模块ID
 * @param {Object} data 模块数据
 * @returns {Promise} 返回编辑结果
 */
export function updateModule(moduleId, data) {
  return request({
    url: `/single/modules/${moduleId}`,
    method: 'put',
    data
  })
}

// ==================== 遥测代号绑定相关接口 ====================

/**
 * 绑定遥测代号
 * @param {Object} data 绑定数据
 * @returns {Promise} 返回绑定结果
 */
export function bindTelemetryCode(data) {
  return request({
    url: '/single/telemetry/bind',
    method: 'post',
    data
  })
}

/**
 * 查询卫星绑定的遥测代号数据
 * @param {number} satelliteId 卫星ID
 * @returns {Promise} 返回遥测代号列表
 */
export function searchTelemetryCodes(satelliteId) {
  return request({
    url: '/single/codesearch',
    method: 'get',
    params: { satelliteId }
  })
}

/**
 * 查询指定层级绑定的遥测代号
 * @param {string} level 层级类型
 * @param {number} id 层级ID
 * @returns {Promise} 返回遥测代号列表
 */
export function getBoundTelemetryCodes(level, id) {
  return request({
    url: '/single/telemetry/bound',
    method: 'get',
    params: { level, id }
  })
}

/**
 * 查询可绑定的遥测代号（根据层级继承规则）
 * @param {string} level 层级类型
 * @param {number} id 层级ID
 * @returns {Promise} 返回可绑定的遥测代号列表
 */
export function getAvailableTelemetryCodes(level, id) {
  return request({
    url: '/single/telemetry/available',
    method: 'get',
    params: { level, id }
  })
}

/**
 * 删除遥测代号绑定
 * @param {string} level 层级类型
 * @param {number} id 层级ID
 * @returns {Promise} 返回删除结果
 */
export function unbindTelemetryCode(level, id) {
  return request({
    url: '/single/telemetry/unbind',
    method: 'delete',
    params: { level, id }
  })
}

/**
 * 删除特定的遥测代号绑定记录
 * @param {number} telemetryCodeId 遥测代号记录ID
 * @returns {Promise} 返回删除结果
 */
export function unbindSpecificTelemetryCode(telemetryCodeId) {
  return request({
    url: `/single/telemetry/unbind/${telemetryCodeId}`,
    method: 'delete'
  })
}

/**
 * 根据层级和遥测代号序号删除绑定
 * @param {string} level 层级类型
 * @param {number} levelId 层级ID
 * @param {number} serialNum 遥测代号序号
 * @returns {Promise} 返回删除结果
 */
export function unbindTelemetryCodeBySerialNum(level, levelId, serialNum) {
  return request({
    url: '/single/telemetry/unbind-by-serial',
    method: 'delete',
    params: { level, levelId, serialNum }
  })
}
