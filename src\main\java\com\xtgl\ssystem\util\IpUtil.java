package com.xtgl.ssystem.util;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * IP地址转换工具类
 */
public class IpUtil {

    /**
     * 将IP字符串转换为字节数组
     *
     * @param ipString IP字符串，如"***********"
     * @return 字节数组
     */
    public static byte[] ipToBytes(String ipString) {
        try {
            return InetAddress.getByName(ipString).getAddress();
        } catch (UnknownHostException e) {
            throw new RuntimeException("IP地址格式错误: " + ipString, e);
        }
    }

    /**
     * 将字节数组转换为IP字符串
     *
     * @param ipBytes 字节数组
     * @return IP字符串，如"***********"
     */
    public static String bytesToIp(byte[] ipBytes) {
        if (ipBytes == null || ipBytes.length != 4) {
            throw new IllegalArgumentException("IP字节数组格式错误");
        }
        try {
            return InetAddress.getByAddress(ipBytes).getHostAddress();
        } catch (UnknownHostException e) {
            throw new RuntimeException("IP字节数组转换错误", e);
        }
    }
} 