import request from './request'

/**
 * 搜索算法
 * @param {Object} params 搜索参数
 * @returns {Promise} 返回搜索结果
 */
export function searchAlgorithms(params) {
  return request({
    url: '/algorithm/search',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取算法详情
 * @param {number} id 算法ID
 * @returns {Promise} 返回算法详情
 */
export function getAlgorithmById(id) {
  return request({
    url: `/algorithm/${id}`,
    method: 'get'
  })
}

/**
 * 更新算法状态
 * @param {number} id 算法ID
 * @param {boolean} enabled 启用状态
 * @returns {Promise} 返回更新结果
 */
export function updateAlgorithmStatus(id, enabled) {
  return request({
    url: '/algorithm/update',
    method: 'put',
    params: { id, enabled }
  })
}
