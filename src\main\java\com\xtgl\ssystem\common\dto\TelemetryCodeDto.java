package com.xtgl.ssystem.common.dto;

import lombok.Data;

/**
 * 遥测代号数据传输对象
 */
@Data
public class TelemetryCodeDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 序号
     */
    private Integer serialNum;

    /**
     * 代号名称
     */
    private String name;

    /**
     * 代号描述
     */
    private String description;

    /**
     * 备注
     */
    private String note;

    /**
     * 所属卫星id
     */
    private Long spacecraftId;

    /**
     * 所属分系统id
     */
    private Long subsystemId;

    /**
     * 所属单机id
     */
    private Long singleId;

    /**
     * 所属模块id
     */
    private Long moduleId;

    /**
     * 所属文件名称
     */
    private String fileName;
    
    /**
     * 代号值（用于前端展示）
     */
    private String code;
    
    /**
     * 单位
     */
    private String unit;


}