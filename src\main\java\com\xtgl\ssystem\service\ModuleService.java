package com.xtgl.ssystem.service;

import com.xtgl.ssystem.common.dto.ModuleDto;
import com.xtgl.ssystem.common.entity.Module;

import java.util.List;

/**
 * 模块服务接口
 */
public interface ModuleService {
    
    /**
     * 添加模块
     * @param moduleDto 模块信息
     * @return 模块ID
     */
    Long addModule(ModuleDto moduleDto);
    
    /**
     * 查询某单机下的所有模块
     * @param singleId 单机ID
     * @return 模块列表
     */
    List<Module> getModulesBySingleId(Long singleId);
    
    /**
     * 删除模块
     * @param moduleId 模块ID
     */
    void deleteModule(Long moduleId);
    
    /**
     * 编辑模块
     * @param moduleId 模块ID
     * @param moduleDto 模块信息
     */
    void updateModule(Long moduleId, ModuleDto moduleDto);
    
    /**
     * 根据ID获取模块
     * @param moduleId 模块ID
     * @return 模块信息
     */
    Module getModuleById(Long moduleId);
    
    /**
     * 根据单机ID删除所有模块
     * @param singleId 单机ID
     */
    void deleteModulesBySingleId(Long singleId);
    
    /**
     * 根据分系统ID删除所有模块
     * @param subsystemId 分系统ID
     */
    void deleteModulesBySubsystemId(Long subsystemId);
}
