-- 算法测试数据
INSERT INTO `algorithm` (`name`, `direction`, `description`, `type`, `enabled`) VALUES
-- 无监督算法 (type = 1)
('K-Means聚类', '数据聚类', 'K-Means是一种经典的无监督学习算法，用于将数据分成K个簇', 1, true),
('层次聚类', '数据聚类', '层次聚类算法通过构建聚类树来进行数据分组', 1, true),
('DBSCAN', '密度聚类', 'DBSCAN是一种基于密度的聚类算法，能够发现任意形状的簇', 1, false),
('主成分分析', '降维分析', 'PCA是一种常用的降维技术，用于数据可视化和特征提取', 1, true),
('独立成分分析', '信号分离', 'ICA用于盲源分离，将混合信号分离成独立的源信号', 1, false),

-- 监督算法 (type = 2)
('随机森林', '分类回归', '随机森林是一种集成学习方法，结合多个决策树进行预测', 2, true),
('支持向量机', '分类回归', 'SVM是一种强大的分类和回归算法，适用于高维数据', 2, true),
('逻辑回归', '分类分析', '逻辑回归是一种线性分类算法，广泛应用于二分类问题', 2, true),
('朴素贝叶斯', '文本分类', '朴素贝叶斯是基于贝叶斯定理的分类算法，常用于文本分析', 2, false),
('梯度提升树', '分类回归', 'GBDT是一种强大的集成学习算法，通过梯度提升提高预测精度', 2, true),

-- 深度学习算法 (type = 3)
('卷积神经网络', '图像识别', 'CNN是专门用于处理图像数据的深度学习模型', 3, true),
('循环神经网络', '序列建模', 'RNN适用于处理序列数据，如时间序列和自然语言', 3, true),
('长短期记忆网络', '序列预测', 'LSTM是RNN的改进版本，能够处理长期依赖问题', 3, true),
('生成对抗网络', '数据生成', 'GAN由生成器和判别器组成，用于生成逼真的数据', 3, false),
('Transformer', '自然语言处理', 'Transformer是目前最先进的自然语言处理模型架构', 3, true);
