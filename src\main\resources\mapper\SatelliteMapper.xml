<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xtgl.ssystem.mapper.SatelliteMapper">

    <!-- 分页条件查询航天器 -->
    <select id="selectPageByCondition" resultType="com.xtgl.ssystem.common.entity.Satellite">
        SELECT s.*
        FROM satellite s
        <where>
            <if test="model != null and model != ''">
                AND s.model LIKE CONCAT('%', #{model}, '%')
            </if>
            <if test="name != null and name != ''">
                AND s.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="header != null and header != ''">
                AND s.header LIKE CONCAT('%', #{header}, '%')
            </if>
            <if test="company != null and company != ''">
                AND s.company LIKE CONCAT('%', #{company}, '%')
            </if>
        </where>
        ORDER BY s.id DESC
    </select>

    <!-- 批量插入批次 -->
    <insert id="batchInsertBatches" parameterType="java.util.List">
        INSERT INTO batch (name)
        VALUES
        <foreach collection="list" item="batch" separator=",">
            (#{batch})
        </foreach>
    </insert>

    <!-- 查询所有批次（按降序排列） -->
    <select id="selectAllBatches" resultType="java.lang.Integer">
        SELECT name FROM batch ORDER BY name DESC
    </select>

    <!-- 根据MQTT名称查找相关联的航天器数量 -->
    <select id="countByMqttName" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM satellite WHERE mqtt_name = #{mqttName}
    </select>

</mapper> 