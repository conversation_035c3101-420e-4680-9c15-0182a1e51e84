package com.xtgl.ssystem.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xtgl.ssystem.common.entity.MqttConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * MQTT配置Mapper接口
 */
@Mapper
public interface MqttConfigMapper extends BaseMapper<MqttConfig> {

    /**
     * 分页查询MQTT配置
     *
     * @param page   分页参数
     * @param status 状态
     * @return 分页结果
     */
    IPage<MqttConfig> selectPageByStatus(Page<MqttConfig> page, @Param("status") Boolean status);
} 