# 算法管理功能使用说明

## 功能概述

算法管理功能提供了对系统中各种算法的分类浏览、搜索和状态管理功能。支持三种算法类型：无监督算法、监督算法、深度学习算法。

## 部署步骤

### 1. 数据库准备

执行以下SQL创建算法表：
```sql
-- 在 ssystem.sql 中已包含此表结构
CREATE TABLE `algorithm` (
    `id`            BIGINT AUTO_INCREMENT COMMENT '主键',
    `name`          VARCHAR(48)  NOT NULL COMMENT '算法名称',
    `direction`     VARCHAR(32)  NOT NULL COMMENT '擅长方向',
    `description`   VARCHAR(255) DEFAULT NULL COMMENT '介绍',
    `type`          TINYINT      NOT NULL COMMENT '算法类型：1-无监督算法 2-监督算法 3-深度学习算法',
    `enabled`       BOOLEAN      DEFAULT TRUE COMMENT '启用状态',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='算法表';
```

### 2. 插入测试数据

执行 `algorithm_test_data.sql` 文件插入测试数据：
```bash
mysql -u username -p database_name < algorithm_test_data.sql
```

### 3. 后端配置

确保以下文件已正确添加到项目中：
- `AlgorithmType.java` - 算法类型枚举
- `Algorithm.java` - 算法实体类
- `AlgorithmDto.java` - 算法DTO
- `AlgorithmSearchDto.java` - 搜索DTO
- `AlgorithmMapper.java` - 数据访问接口
- `AlgorithmMapper.xml` - SQL映射文件
- `AlgorithmService.java` - 服务接口
- `AlgorithmServiceImpl.java` - 服务实现
- `AlgorithmController.java` - 控制器

### 4. 前端配置

确保以下文件已正确配置：
- `algorithm.js` - API接口封装
- `AlgorithmManagement.vue` - 页面组件
- `vite.config.js` - 代理配置已添加 `/algorithm` 路径

## 使用指南

### 1. 访问页面

在浏览器中访问：`http://localhost:5175/spacecraft/algorithm`

### 2. 页面功能

#### 2.1 算法类型切换
- 页面顶部有三个连在一起的按钮
- 默认选中"无监督算法"
- 点击不同按钮可切换算法类型
- 选中状态：蓝底白字，未选中：灰底黑字

#### 2.2 搜索功能
- 在"算法名称"输入框中输入关键字
- 点击输入框右侧的搜索图标或"搜索"按钮执行搜索
- 搜索范围限定在当前选中的算法类型内
- 点击"重置"按钮清空搜索条件

#### 2.3 数据表格
表格包含以下列：
- **算法名称**: 显示算法的完整名称
- **擅长方向**: 显示算法的应用领域
- **介绍**: 显示算法的详细描述
- **启停状态**: 用彩色标签显示（绿色=启用，红色=停用）
- **操作**: 包含"编辑"按钮

#### 2.4 编辑功能
1. 点击表格中的"编辑"按钮
2. 弹出编辑对话框
3. 对话框中显示算法的所有信息
4. 只有"启用状态"可以修改（单选按钮：启用/停用）
5. 其他字段为只读状态
6. 点击"确定"保存修改，点击"取消"放弃修改

#### 2.5 分页功能
- 页面底部有完整的分页组件
- 支持选择每页显示条数：10/20/50/100
- 支持页码跳转和上下页导航
- 显示总记录数和当前页信息

## 测试数据说明

系统提供了15条测试数据，分布如下：

### 无监督算法 (5条)
- K-Means聚类
- 层次聚类
- DBSCAN
- 主成分分析
- 独立成分分析

### 监督算法 (5条)
- 随机森林
- 支持向量机
- 逻辑回归
- 朴素贝叶斯
- 梯度提升树

### 深度学习算法 (5条)
- 卷积神经网络
- 循环神经网络
- 长短期记忆网络
- 生成对抗网络
- Transformer

## 常见问题

### Q1: 页面显示"暂无数据"
**解决方案**: 
1. 检查数据库中是否有算法数据
2. 确认后端服务是否正常启动
3. 检查浏览器控制台是否有错误信息

### Q2: 搜索功能不工作
**解决方案**:
1. 确认输入的关键字是否正确
2. 检查当前选中的算法类型是否包含相关数据
3. 尝试点击"重置"按钮后重新搜索

### Q3: 编辑功能保存失败
**解决方案**:
1. 检查网络连接是否正常
2. 确认后端服务是否正常运行
3. 查看浏览器控制台的错误信息

### Q4: 分页功能异常
**解决方案**:
1. 刷新页面重试
2. 检查数据总数是否正确
3. 确认分页参数是否在有效范围内

## 技术支持

如遇到其他问题，请检查：
1. 后端日志文件中的错误信息
2. 浏览器开发者工具中的网络请求
3. 数据库连接和数据完整性

## 更新日志

- v1.0.0: 初始版本，支持基本的搜索、查看、编辑功能
- 支持三种算法类型的分类浏览
- 支持按名称模糊搜索
- 支持启用状态的修改
- 支持分页浏览
