package com.xtgl.ssystem.controller;

import com.xtgl.ssystem.common.dto.SatelliteDto;
import com.xtgl.ssystem.common.dto.SatelliteSearchDto;
import com.xtgl.ssystem.common.entity.PageResult;
import com.xtgl.ssystem.common.entity.Result;
import com.xtgl.ssystem.service.SatelliteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;
import com.xtgl.ssystem.common.dto.TelemetryCodeDto;

/**
 * 航天器控制器
 */
@RestController
@RequestMapping("/satellite")
public class SatelliteController {

    @Autowired
    private SatelliteService satelliteService;

    /**
     * 新建航天器
     *
     * @param satelliteDto 航天器DTO
     * @return 新建的航天器DTO，包含ID和创建时间
     */
    @PostMapping("/new")
    public Result<Map<String, Object>> createSatellite(@RequestBody @Validated SatelliteDto satelliteDto) {
        Map<String, Object> result = satelliteService.createSatellite(satelliteDto);
        return Result.success(result);
    }

    /**
     * 批量删除航天器
     *
     * @param id  航天器ID列表，多个ID用英文逗号分隔
     * @param all 是否删除所有
     * @return 删除数量
     */
    @DeleteMapping("/delete")
    public Result<Map<String, Object>> deleteSatellites(
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "all", required = false, defaultValue = "false") boolean all) {
        
        if (!all && !StringUtils.hasText(id)) {
            return Result.error(400, "请指定待删除的ID或设置all=true");
        }
        
        List<Long> ids = new ArrayList<>();
        if (StringUtils.hasText(id)) {
            ids = Arrays.stream(id.split(","))
                    .map(String::trim)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        }
        
        int deleted = satelliteService.deleteSatellites(ids, all);
        Map<String, Object> result = new HashMap<>();
        result.put("deleted", deleted);
        return Result.success(result);
    }

    /**
     * 分页条件查询航天器
     *
     * @param model    型号（模糊）
     * @param name     名称（模糊）
     * @param header   负责人（模糊）
     * @param company  所属单位（模糊）
     * @param pageNo   页码，默认1
     * @param pageSize 每页条数，默认10
     * @return 分页结果
     */
    @GetMapping("/search")
    public Result<PageResult<SatelliteDto>> searchSatellites(
            @RequestParam(value = "model", required = false) String model,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "header", required = false) String header,
            @RequestParam(value = "company", required = false) String company,
            @RequestParam(value = "pageNo", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        
        SatelliteSearchDto searchDto = new SatelliteSearchDto();
        searchDto.setModel(model);
        searchDto.setName(name);
        searchDto.setHeader(header);
        searchDto.setCompany(company);
        searchDto.setPageNo(pageNo);
        searchDto.setPageSize(pageSize);
        
        PageResult<SatelliteDto> pageResult = satelliteService.searchSatellites(searchDto);
        return Result.success(pageResult);
    }

    /**
     * 根据ID获取航天器
     *
     * @param id 航天器ID
     * @return 航天器DTO
     */
    @GetMapping("/{id}")
    public Result<SatelliteDto> getSatelliteById(@PathVariable("id") Long id) {
        SatelliteDto satelliteDto = satelliteService.getSatelliteById(id);
        if (satelliteDto != null) {
            return Result.success(satelliteDto);
        } else {
            return Result.error(404, "航天器不存在");
        }
    }

    /**
     * 更新航天器
     *
     * @param satelliteDto 航天器DTO
     * @return 更新后的航天器DTO
     */
    @PutMapping("/update")
    public Result<SatelliteDto> updateSatellite(@RequestBody @Validated SatelliteDto satelliteDto) {
        if (satelliteDto.getId() == null) {
            return Result.error(400, "ID不能为空");
        }
        SatelliteDto result = satelliteService.updateSatellite(satelliteDto);
        return Result.success(result);
    }

    /**
     * 添加批次
     *
     * @param batches 批次或批次列表
     * @return 插入数量
     */
    @PostMapping("/batch")
    public Result<Map<String, Object>> addBatches(@RequestBody Object batches) {
        List<Integer> batchList = new ArrayList<>();
        
        if (batches instanceof Integer) {
            batchList.add((Integer) batches);
        } else if (batches instanceof List) {
            batchList = (List<Integer>) batches;
        } else {
            return Result.error(400, "请提供有效的批次号数据");
        }
        
        // 验证批次号范围：1-9999
        for (Integer batch : batchList) {
            if (batch < 1 || batch > 9999) {
                return Result.error(400, "批次号必须在1-9999范围内");
            }
        }
        
        int inserted = satelliteService.addBatches(batchList);
        Map<String, Object> result = new HashMap<>();
        result.put("inserted", inserted);
        return Result.success(result);
    }

    /**
     * 查询批次
     *
     * @return 批次列表
     */
    @GetMapping("/batch")
    public Result<List<Integer>> getAllBatches() {
        List<Integer> batches = satelliteService.getAllBatches();
        return Result.success(batches);
    }

    /**
     * MQTT连接测试
     *
     * @param mqttName MQTT名称
     * @return 连接测试结果
     */
    @PostMapping("/mqtttest")
    public Result<Object> testMqttConnection(@RequestBody Map<String, String> params) {
        String mqttName = params.get("mqtt_name");
        if (!StringUtils.hasText(mqttName)) {
            return Result.error(400, "MQTT名称不能为空");
        }
        
        try {
            return Result.success(satelliteService.testMqttConnection(mqttName));
        } catch (IllegalArgumentException e) {
            return Result.error(404, e.getMessage());
        } catch (Exception e) {
            return Result.error(500, "MQTT连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有MQTT连接名称
     *
     * @return MQTT连接名称列表
     */
    @GetMapping("/mqtttest")
    public Result<List<String>> getAllMqttNames() {
        List<String> mqttNames = satelliteService.getAllMqttNames();
        return Result.success(mqttNames);
    }

    /**
     * 上传遥测代号数据文件（Excel），不绑定航天器，仅检查文件格式
     *
     * @param file Excel文件
     * @return 上传结果
     */
    @PostMapping("/upload/file")
    public Result<Map<String, Object>> uploadTelemetryCodeFile(
            @RequestParam("file") MultipartFile file) {

        if (file.isEmpty()) {
            return Result.error(400, "请选择文件上传");
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            return Result.error(400, "仅支持.xlsx或.xls格式的Excel文件");
        }

        try {
            Map<String, Object> result = satelliteService.uploadTelemetryCodeFile(file);
            return Result.success(result);
        } catch (IllegalArgumentException e) {
            return Result.error(422, e.getMessage());
        } catch (Exception e) {
            return Result.error(500, "上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传遥测代号数据文件（Excel），使用文件名作为批次标识
     *
     * @param file     Excel文件
     * @param fileName 文件名（批次标识）
     * @return 上传结果
     */
    @PostMapping("/upload/batch")
    public Result<Map<String, Object>> uploadTelemetryCodeFileWithBatch(
            @RequestParam("file") MultipartFile file,
            @RequestParam("fileName") String fileName) {

        if (file.isEmpty()) {
            return Result.error(400, "请选择文件上传");
        }

        if (!StringUtils.hasText(fileName)) {
            return Result.error(400, "文件名（批次标识）不能为空");
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            return Result.error(400, "仅支持.xlsx或.xls格式的Excel文件");
        }

        try {
            Map<String, Object> result = satelliteService.uploadTelemetryCodeFileWithBatch(file, fileName);
            return Result.success(result);
        } catch (IllegalArgumentException e) {
            return Result.error(422, e.getMessage());
        } catch (Exception e) {
            return Result.error(500, "上传失败: " + e.getMessage());
        }
    }

    /**
     * 导入遥测代号数据（Excel），绑定到指定航天器
     *
     * @param file        Excel文件
     * @param satelliteId 航天器ID
     * @return 导入结果
     */
    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadTelemetryCodes(
            @RequestParam("file") MultipartFile file,
            @RequestParam("satelliteId") Long satelliteId) {
        
        if (file.isEmpty()) {
            return Result.error(400, "请选择文件上传");
        }
        
        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            return Result.error(400, "仅支持.xlsx或.xls格式的Excel文件");
        }
        
        try {
            Map<String, Object> result = satelliteService.uploadTelemetryCodes(file, satelliteId);
            return Result.success(result);
        } catch (IllegalArgumentException e) {
            return Result.error(422, e.getMessage());
        } catch (Exception e) {
            return Result.error(500, "导入失败: " + e.getMessage());
        }
    }



    /**
     * 查询航天器遥测代号
     *
     * @param id          遥测代号ID
     * @param satelliteId 航天器ID
     * @return 遥测代号列表
     */
    @GetMapping("/telemetry")
    public Result<List<TelemetryCodeDto>> getTelemetryCodes(
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "satelliteId", required = false) Long satelliteId) {
        
        if (id == null && satelliteId == null) {
            return Result.error(400, "请提供id或satelliteId参数");
        }
        
        List<TelemetryCodeDto> telemetryCodeDtos = satelliteService.getTelemetryCodesDto(id, satelliteId);
        return Result.success(telemetryCodeDtos);
    }

    /**
     * 删除遥测代号数据
     *
     * @param id  遥测代号ID列表，多个ID用英文逗号分隔
     * @param all 是否删除所有
     * @return 删除数量
     */
    @DeleteMapping("/telemetry")
    public Result<Map<String, Object>> deleteTelemetryCodes(
            @RequestParam(value = "id", required = false) String id,
            @RequestParam(value = "all", required = false, defaultValue = "false") boolean all) {
        
        if (!all && !StringUtils.hasText(id)) {
            return Result.error(400, "请指定待删除的ID或设置all=true");
        }
        
        try {
            List<Long> ids = new ArrayList<>();
            if (StringUtils.hasText(id)) {
                ids = Arrays.stream(id.split(","))
                        .map(String::trim)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
            }
            
            int deleted = 0;
            if (ids.size() == 1) {
                // 单个删除
                boolean success = satelliteService.deleteTelemetryCode(ids.get(0));
                deleted = success ? 1 : 0;
            } else {
                // 批量删除
                deleted = satelliteService.deleteTelemetryCodes(ids);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("deleted", deleted);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(500, "删除失败: " + e.getMessage());
        }
    }






    

    


    /**
     * 根据文件名（批次标识）查询未绑定航天器的遥测代号数据
     *
     * @param fileName 文件名（批次标识）
     * @return 遥测代号列表
     */
    @GetMapping("/telemetry/batch")
    public Result<List<TelemetryCodeDto>> getTelemetryCodesByFileName(
            @RequestParam("fileName") String fileName) {

        if (!StringUtils.hasText(fileName)) {
            return Result.error(400, "文件名不能为空");
        }

        try {
            List<TelemetryCodeDto> telemetryCodeDtos = satelliteService.getTelemetryCodesByFileName(fileName);
            return Result.success(telemetryCodeDtos);
        } catch (IllegalArgumentException e) {
            return Result.error(422, e.getMessage());
        } catch (Exception e) {
            return Result.error(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件名（批次标识）删除临时遥测代号数据
     *
     * @param fileName 文件名（批次标识）
     * @return 删除结果
     */
    @DeleteMapping("/telemetry/batch")
    public Result<Map<String, Object>> deleteTelemetryCodesByFileName(
            @RequestParam("fileName") String fileName) {

        if (!StringUtils.hasText(fileName)) {
            return Result.error(400, "文件名不能为空");
        }

        try {
            Map<String, Object> result = satelliteService.deleteTelemetryCodesByFileName(fileName);
            return Result.success(result);
        } catch (IllegalArgumentException e) {
            return Result.error(422, e.getMessage());
        } catch (Exception e) {
            return Result.error(500, "删除失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件名（批次标识）更新航天器ID关联
     *
     * @param satelliteId 航天器ID
     * @param fileName    文件名（批次标识）
     * @return 更新结果
     */
    @PostMapping("/telemetry/batch/bind")
    public Result<Map<String, Object>> updateSatelliteIdByFileName(
            @RequestParam("satelliteId") Long satelliteId,
            @RequestParam("fileName") String fileName) {

        if (satelliteId == null) {
            return Result.error(400, "航天器ID不能为空");
        }

        if (!StringUtils.hasText(fileName)) {
            return Result.error(400, "文件名不能为空");
        }

        try {
            Map<String, Object> result = satelliteService.updateSatelliteIdByFileName(satelliteId, fileName);
            return Result.success(result);
        } catch (IllegalArgumentException e) {
            return Result.error(422, e.getMessage());
        } catch (Exception e) {
            return Result.error(500, "更新关联失败: " + e.getMessage());
        }
    }

    /**
     * 获取航天器已绑定的文件信息
     *
     * @param satelliteId 航天器ID
     * @return 文件信息
     */
    @GetMapping("/bound-file/{satelliteId}")
    public Result<Map<String, Object>> getBoundFileInfo(@PathVariable("satelliteId") Long satelliteId) {
        if (satelliteId == null) {
            return Result.error(400, "航天器ID不能为空");
        }

        try {
            Map<String, Object> result = satelliteService.getBoundFileInfo(satelliteId);
            return Result.success(result);
        } catch (IllegalArgumentException e) {
            return Result.error(422, e.getMessage());
        } catch (Exception e) {
            return Result.error(500, "获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 删除航天器已绑定的遥测数据
     *
     * @param satelliteId 航天器ID
     * @return 删除结果
     */
    @DeleteMapping("/bound-data/{satelliteId}")
    public Result<Map<String, Object>> deleteBoundTelemetryData(@PathVariable("satelliteId") Long satelliteId) {
        if (satelliteId == null) {
            return Result.error(400, "航天器ID不能为空");
        }

        try {
            Map<String, Object> result = satelliteService.deleteBoundTelemetryData(satelliteId);
            return Result.success(result);
        } catch (IllegalArgumentException e) {
            return Result.error(422, e.getMessage());
        } catch (Exception e) {
            return Result.error(500, "删除遥测数据失败: " + e.getMessage());
        }
    }
}