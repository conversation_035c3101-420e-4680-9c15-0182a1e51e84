<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xtgl.ssystem.mapper.BatchMapper">

    <!-- 批量插入批次 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO batch (name)
        VALUES
        <foreach collection="list" item="batch" separator=",">
            (#{batch})
        </foreach>
    </insert>

    <!-- 查询所有批次号（按降序排列） -->
    <select id="selectAllBatchNames" resultType="java.lang.Integer">
        SELECT name FROM batch ORDER BY name DESC
    </select>

</mapper> 