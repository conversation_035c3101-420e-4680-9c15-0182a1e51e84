package com.xtgl.ssystem.service;

import com.xtgl.ssystem.common.dto.TelemetryBindDto;
import com.xtgl.ssystem.common.dto.TelemetryBindResponseDto;
import com.xtgl.ssystem.common.entity.TelemetryCode;

import java.util.List;

/**
 * 遥测代号绑定服务接口
 */
public interface TelemetryBindService {

    /**
     * 绑定遥测代号
     *
     * @param bindDto 绑定请求
     * @return 绑定响应
     */
    TelemetryBindResponseDto bindTelemetryCode(TelemetryBindDto bindDto);

    /**
     * 查询指定层级绑定的遥测代号
     *
     * @param level 层级类型
     * @param id    层级ID
     * @return 遥测代号列表
     */
    List<TelemetryCode> getBoundTelemetryCodes(String level, Long id);

    /**
     * 查询可绑定的遥测代号（根据层级继承规则）
     *
     * @param level 层级类型
     * @param id    层级ID
     * @return 可绑定的遥测代号列表
     */
    List<TelemetryCode> getAvailableTelemetryCodes(String level, Long id);

    /**
     * 删除遥测代号绑定
     *
     * @param level 层级类型
     * @param id    层级ID
     * @return 删除数量
     */
    int unbindTelemetryCode(String level, Long id);

    /**
     * 根据层级和遥测代号序号删除绑定
     *
     * @param level     层级类型
     * @param levelId   层级ID
     * @param serialNum 遥测代号序号
     * @return 删除数量
     */
    int unbindTelemetryCodeBySerialNum(String level, Long levelId, Integer serialNum);

    /**
     * 删除特定的遥测代号绑定记录
     *
     * @param telemetryCodeId 遥测代号记录ID
     * @return 删除数量
     */
    int unbindSpecificTelemetryCode(Long telemetryCodeId);
}
