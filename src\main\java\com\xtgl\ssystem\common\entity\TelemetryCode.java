package com.xtgl.ssystem.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 遥测代号实体类
 */
@Data
@TableName("telemetry_code")
public class TelemetryCode {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 序号
     */
    @TableField("serial_num")
    private Integer serialNum;

    /**
     * 代号名称
     */
    @TableField("name")
    private String name;

    /**
     * 代号描述
     */
    @TableField("description")
    private String description;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 所属卫星id
     */
    @TableField("spacecraft_id")
    private Long spacecraftId;

    /**
     * 所属分系统id
     */
    @TableField("subsystem_id")
    private Long subsystemId;

    /**
     * 所属单机id
     */
    @TableField("single_id")
    private Long singleId;

    /**
     * 所属模块id
     */
    @TableField("module_id")
    private Long moduleId;

    /**
     * 所属文件名称
     */
    @TableField("file_name")
    private String fileName;
    

} 