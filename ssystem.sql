CREATE DATABASE IF NOT EXISTS satellite_system
  DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;

USE satellite_system;

CREATE TABLE `mqtt_table` (
    `id`            BIGINT AUTO_INCREMENT COMMENT '主键',
    `name`          VARCHAR(64) NOT NULL COMMENT '名称',
    `ip`            VARBINARY(16) NOT NULL COMMENT 'ip地址',
    `port`          SMALLINT UNSIGNED NOT NULL COMMENT '端口',
    `topic`         VARCHAR(255) NOT NULL COMMENT '主题',
    `user_name`     VARCHAR(50) NOT NULL COMMENT '用户名',
    `password`      VARCHAR(255) NOT NULL COMMENT '密码',
    `note`          VARCHAR(255) DEFAULT NULL COMMENT '备注',
    `status`        BOOLEAN DEFAULT TRUE COMMENT '启用状态',
    `update_person` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
    `update_time`   DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='MQTT配置表';



CREATE TABLE `batch` (
    `id`   BIGINT AUTO_INCREMENT COMMENT '主键',
    `name` INT NOT NULL COMMENT '名称（批次号）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批次表';			

CREATE TABLE `module` (
    `id`            BIGINT AUTO_INCREMENT COMMENT '主键',
    `name`          VARCHAR(32) NOT NULL COMMENT '名称',
    `subsystem_id`  BIGINT NOT NULL COMMENT '所属分系统id',
    `spacecraft_id` BIGINT NOT NULL COMMENT '所属卫星id',
    `single_id`     BIGINT NOT NULL COMMENT '所属单机id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模块表';

CREATE TABLE `satellite` (
    `id`                BIGINT AUTO_INCREMENT COMMENT '主键',
    `model`             VARCHAR(64)  NOT NULL COMMENT '型号',
    `name`              VARCHAR(64)  NOT NULL COMMENT '名称',
    `header`            VARCHAR(48)  NOT NULL COMMENT '负责人',
    `create_time`       DATE         NOT NULL COMMENT '创建时间',
    `company`           VARCHAR(100) NOT NULL COMMENT '所属单位',
    `receive_time`      DATETIME     DEFAULT NULL COMMENT '开始接收时间',
    `status`            VARCHAR(24)  NOT NULL COMMENT '卫星状态（4种）',
    `batch`             INT          NOT NULL COMMENT '批次',
    `mqtt_name`         VARCHAR(64)  DEFAULT NULL COMMENT 'MQTT名称',
    `telemetry_code_id` BIGINT       DEFAULT NULL COMMENT '遥测代号id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='航天器信息表';

CREATE TABLE `single` (
    `id`            BIGINT AUTO_INCREMENT COMMENT '主键',
    `name`          VARCHAR(32) NOT NULL COMMENT '名称',
    `subsystem_id`  BIGINT NOT NULL COMMENT '所属分系统id',
    `spacecraft_id` BIGINT NOT NULL COMMENT '所属卫星id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单机表';

CREATE TABLE `subsystem` (
    `id`            BIGINT AUTO_INCREMENT COMMENT '主键',
    `name`          VARCHAR(64) NOT NULL COMMENT '名称',
    `spacecraft_id` BIGINT      NOT NULL COMMENT '所属卫星id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分系统表';

create table telemetry_code
(
    id                  bigint auto_increment comment '主键' primary key,
    serial_num          int          not null comment '序号',
    name                varchar(64)  not null comment '代号名称',
    description         varchar(255) null comment '代号描述',
    note                varchar(255) null comment '备注',
    subsystem_id        bigint       null comment '所属卫星id',
    spacecraft_id       bigint       null comment '所属分系统id',
    single_id           bigint       null comment '所属单机id',
    module_id           bigint       null comment '所属模块id',
    file_name           varchar(255) null comment '所属文件名称',
)
    comment '遥测代号表';

CREATE TABLE `algorithm` (
    `id`            BIGINT AUTO_INCREMENT COMMENT '主键',
    `name`          VARCHAR(48)  NOT NULL COMMENT '算法名称',
    `direction`     VARCHAR(32)  NOT NULL COMMENT '擅长方向',
    `description`   VARCHAR(255) DEFAULT NULL COMMENT '介绍',
    `type`          TINYINT      NOT NULL COMMENT '算法类型：1-无监督算法 2-监督算法 3-深度学习算法',
    `enabled`       BOOLEAN      DEFAULT TRUE COMMENT '启用状态',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='算法表';