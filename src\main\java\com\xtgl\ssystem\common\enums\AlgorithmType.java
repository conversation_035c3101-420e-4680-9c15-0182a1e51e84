package com.xtgl.ssystem.common.enums;

/**
 * 算法类型枚举
 */
public enum AlgorithmType {
    
    /**
     * 无监督算法
     */
    UNSUPERVISED(1, "无监督算法"),
    
    /**
     * 监督算法
     */
    SUPERVISED(2, "监督算法"),
    
    /**
     * 深度学习算法
     */
    DEEP_LEARNING(3, "深度学习算法");
    
    private final Integer code;
    private final String description;
    
    AlgorithmType(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static AlgorithmType fromCode(Integer code) {
        for (AlgorithmType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的算法类型代码: " + code);
    }
    
    /**
     * 根据名称获取枚举
     */
    public static AlgorithmType fromName(String name) {
        for (AlgorithmType type : values()) {
            if (type.name().equals(name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的算法类型名称: " + name);
    }
}
