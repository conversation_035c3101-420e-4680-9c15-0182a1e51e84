import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    component: () => import('@/views/layout/Layout.vue'),
    redirect: '/spacecraft/list',
    children: [
      // 航天器管理
      {
        path: '/spacecraft/list',
        name: 'SpacecraftList',
        component: () => import('@/views/spacecraft/SpacecraftList.vue'),
        meta: { title: '航天器列表' }
      },
      {
        path: '/spacecraft/telemetry/:id',
        name: 'SpacecraftTelemetry',
        component: () => import('@/views/spacecraft/SpacecraftTelemetry.vue'),
        meta: { title: '遥测代号查看' }
      },
      {
        path: '/spacecraft/create',
        name: 'SpacecraftCreate',
        component: () => import('@/views/spacecraft/SpacecraftCreate.vue'),
        meta: { title: '新建航天器' }
      },
      {
        path: '/spacecraft/upload/:id',
        name: 'SpacecraftUpload',
        component: () => import('@/views/spacecraft/SpacecraftUpload.vue'),
        meta: { title: '文件上传' }
      },
      {
        path: '/spacecraft/config',
        name: 'SpacecraftConfig',
        component: () => import('@/views/spacecraft/SpacecraftConfig.vue'),
        meta: { title: '单机配置' }
      },
      {
        path: '/spacecraft/config-main',
        name: 'SpacecraftConfigMain',
        component: () => import('@/views/spacecraft/ConfigMain.vue'),
        meta: { title: '配置遥测代号' }
      },

      {
        path: '/spacecraft/algorithm',
        name: 'AlgorithmManagement',
        component: () => import('@/views/spacecraft/AlgorithmManagement.vue'),
        meta: { title: '算法管理' }
      },
      // 权限管理
      {
        path: '/auth/user',
        name: 'UserManagement',
        component: () => import('@/views/auth/UserManagement.vue'),
        meta: { title: '用户管理' }
      },
      {
        path: '/auth/role',
        name: 'RoleManagement',
        component: () => import('@/views/auth/RoleManagement.vue'),
        meta: { title: '角色管理' }
      },
      // 系统管理
      {
        path: '/system/mqtt',
        name: 'MqttManagement',
        component: () => import('@/views/system/MqttManagement.vue'),
        meta: { title: 'MQTT管理' }
      },
      {
        path: '/system/log',
        name: 'OperationLog',
        component: () => import('@/views/system/OperationLog.vue'),
        meta: { title: '操作日志' }
      },
      // CORS测试页面
      {
        path: '/cors-test',
        name: 'CorsTest',
        component: () => import('@/views/CorsTest.vue'),
        meta: { title: 'CORS测试' }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router 