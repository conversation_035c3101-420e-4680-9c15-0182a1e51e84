package com.xtgl.ssystem.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * MQTT配置实体类
 */
@Data
@TableName("mqtt_table")
public class MqttConfig {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * IP地址
     */
    @TableField("ip")
    private byte[] ip;

    /**
     * 端口
     */
    @TableField("port")
    private Integer port;

    /**
     * 主题
     */
    @TableField("topic")
    private String topic;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 备注
     */
    @TableField("note")
    private String note;

    /**
     * 启用状态
     */
    @TableField("status")
    private Boolean status;

    /**
     * 更新人
     */
    @TableField("update_person")
    private String updatePerson;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
} 