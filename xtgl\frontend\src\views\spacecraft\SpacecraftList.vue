<template>
  <div class="spacecraft-container">
    <h2 class="page-title">航天器列表</h2>
    
    <!-- 搜索过滤区域 -->
    <div class="filter-container">
      <el-row :gutter="20">
        <el-col :span="5">
          <div class="filter-item">
            <span>航天器型号：</span>
            <el-input
              v-model="searchForm.model"
              placeholder="请输入型号"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #suffix>
                <el-button :icon="Search" circle @click="handleSearchByField('model')" />
              </template>
            </el-input>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="filter-item">
            <span>航天器名称：</span>
            <el-input
              v-model="searchForm.name"
              placeholder="请输入名称"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #suffix>
                <el-button :icon="Search" circle @click="handleSearchByField('name')" />
              </template>
            </el-input>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="filter-item">
            <span>负责人名称：</span>
            <el-input
              v-model="searchForm.header"
              placeholder="请输入负责人"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #suffix>
                <el-button :icon="Search" circle @click="handleSearchByField('header')" />
              </template>
            </el-input>
          </div>
        </el-col>
        <el-col :span="5">
          <div class="filter-item">
            <span>所属单位：</span>
            <el-input
              v-model="searchForm.company"
              placeholder="请输入单位"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #suffix>
                <el-button :icon="Search" circle @click="handleSearchByField('company')" />
              </template>
            </el-input>
          </div>
        </el-col>
        <el-col :span="4" class="right-buttons">
          <el-button type="primary" @click="handleSearch">
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-col>
      </el-row>
    </div>
    
    <!-- 操作按钮区 -->
    <div class="action-container">
      <div class="action-left">
        <el-button type="primary" @click="handleCreate">
          新建航天器
        </el-button>
        <el-button type="danger" @click="handleBatchDelete" :disabled="!selectedRows.length">
          批量删除
        </el-button>
        <el-button @click="handleExport">
          导出列表
        </el-button>
      </div>
    </div>
    
    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="model" label="型号" />
      <el-table-column prop="name" label="名称" />
      <el-table-column prop="header" label="负责人" />
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column prop="company" label="所属单位" />
      <el-table-column label="开始接收时间" width="180">
        <template #default="scope">
          <span>{{ formatReceiveTime(scope.row.receiveTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="卫星状态">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status || '未开始' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="遥测代号" width="100" align="center">
        <template #default="scope">
          <el-button type="primary" link @click="viewTelemetry(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="handleEdit(scope.row)"
          >
            编辑
          </el-button>
          <el-button
            type="primary"
            link
            @click="handleImport(scope.row)"
          >
            导入
          </el-button>
          <el-button
            type="danger"
            link
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.pageNo"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="Number(pagination.total)"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialog.visible"
      title="确认删除"
      width="30%"
    >
      <span>{{ deleteDialog.message }}</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmDelete" :loading="deleteDialog.loading">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { searchSatellites, deleteSatellite } from '@/api/satellite'
import { exportSatelliteList } from '@/utils/export'

const router = useRouter()
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  model: '',
  name: '',
  header: '',
  company: ''
})

// 分页配置
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

// 删除对话框
const deleteDialog = reactive({
  visible: false,
  message: '',
  loading: false,
  type: 'single', // single or batch
  id: null
})

// 初始化数据
onMounted(() => {
  fetchData()
})

// 获取表格数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize
    }

    const res = await searchSatellites(params)

    // 确保数据结构正确
    if (res && res.data) {
      tableData.value = res.data.list || []
      // 确保total是数字类型
      pagination.total = Number(res.data.total) || 0
    } else {
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取航天器列表失败', error)
    ElMessage.error('获取数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.pageNo = 1
  fetchData()
}

// 根据单个字段搜索
const handleSearchByField = (field) => {
  pagination.pageNo = 1
  
  // 其他字段清空
  Object.keys(searchForm).forEach(key => {
    if (key !== field) {
      searchForm[key] = ''
    }
  })
  
  fetchData()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.pageNo = 1
  fetchData()
}

// 处理表格选择变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

// 处理创建航天器
const handleCreate = () => {
  router.push('/spacecraft/create')
}

// 查看遥测代号
const viewTelemetry = (row) => {
  router.push({
    path: `/spacecraft/telemetry/${row.id}`,
    query: { from: 'list' }
  })
}

// 处理编辑航天器
const handleEdit = (row) => {
  // 跳转到编辑页面
  router.push({
    path: '/spacecraft/create',
    query: { id: row.id }
  })
}

// 处理导入遥测代号
const handleImport = (row) => {
  // 直接跳转到文件上传页面
  router.push({
    path: `/spacecraft/upload/${row.id}`,
    query: {
      name: row.name,
      model: row.model,
      company: row.company,
      header: row.header,
      batch: row.batch,
      mqttName: row.mqttName
    }
  })
}

// 处理删除单个航天器
const handleDelete = (row) => {
  deleteDialog.visible = true
  deleteDialog.message = `确定要删除航天器 "${row.name}" 吗？删除后不可恢复。`
  deleteDialog.type = 'single'
  deleteDialog.id = row.id
}

// 处理批量删除航天器
const handleBatchDelete = () => {
  if (!selectedRows.value.length) {
    ElMessage.warning('请先选择要删除的航天器')
    return
  }
  
  deleteDialog.visible = true
  deleteDialog.message = `确定要删除选中的 ${selectedRows.value.length} 个航天器吗？删除后不可恢复。`
  deleteDialog.type = 'batch'
}

// 确认删除
const confirmDelete = async () => {
  deleteDialog.loading = true
  try {
    let id
    
    if (deleteDialog.type === 'single') {
      id = deleteDialog.id
    } else {
      id = selectedRows.value.map(row => row.id).join(',')
    }
    
    await deleteSatellite(id)
    
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    console.error('删除航天器失败', error)
    ElMessage.error('删除失败')
  } finally {
    deleteDialog.loading = false
    deleteDialog.visible = false
  }
}

// 处理导出列表
const handleExport = async () => {
  try {
    if (tableData.value.length === 0) {
      ElMessage.warning('没有数据可以导出')
      return
    }

    ElMessage.info('正在准备导出，请稍候...')

    // 获取所有数据（如果当前只是分页数据，需要获取全部数据）
    let exportData = tableData.value

    // 如果有搜索条件，获取所有符合条件的数据
    if (pagination.total > tableData.value.length) {
      const params = {
        ...searchForm,
        pageNo: 1,
        pageSize: pagination.total // 获取所有数据
      }

      const res = await searchSatellites(params)
      exportData = res.data.list || []
    }

    // 准备搜索参数用于文件名
    const searchParams = {}
    if (searchForm.model && searchForm.model.trim()) {
      searchParams.model = searchForm.model.trim()
    }
    if (searchForm.name && searchForm.name.trim()) {
      searchParams.name = searchForm.name.trim()
    }
    if (searchForm.header && searchForm.header.trim()) {
      searchParams.header = searchForm.header.trim()
    }
    if (searchForm.company && searchForm.company.trim()) {
      searchParams.company = searchForm.company.trim()
    }

    // 使用前端导出
    await exportSatelliteList(exportData, searchParams)

    ElMessage.success('导出成功！')
  } catch (error) {
    console.error('导出失败', error)
    ElMessage.error('导出失败：' + error.message)
  }
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchData()
}

// 分页页码变化
const handleCurrentChange = (val) => {
  pagination.pageNo = val
  fetchData()
}

// 格式化接收时间
const formatReceiveTime = (receiveTime) => {
  if (!receiveTime) {
    return '未开始'
  }

  try {
    const date = new Date(receiveTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return receiveTime
  }
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case '未开始':
      return 'info'
    case '接收中':
      return 'success'
    case '已暂停':
      return 'warning'
    case '已结束':
      return 'danger'
    default:
      return 'info'
  }
}
</script>

<style scoped>
.spacecraft-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  height: 100%;
}

.page-title {
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.filter-container {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-item span {
  white-space: nowrap;
  margin-right: 5px;
}

.right-buttons {
  display: flex;
  justify-content: flex-end;
}

.action-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 