# 多对象绑定同一遥测代号实现方案

## 🎯 需求分析

**问题**：多个模块、多个分系统、多个单机都需要绑定同一个遥测代号，但在不改变数据库结构的情况下实现。

**解决方案**：为每个绑定关系创建独立的遥测代号记录副本，通过复制原始遥测代号信息来实现多对象绑定。

## 🏗️ 实现原理

### 1. 绑定逻辑

当绑定遥测代号时：
1. 根据遥测代号ID查找原始遥测代号记录
2. 创建新的遥测代号记录副本
3. 复制原始记录的基本信息（序号、名称、描述、备注等）
4. 设置新记录的层级ID字段（subsystem_id、single_id、module_id）
5. 保持层级关系的完整性

### 2. 层级关系维护

```
航天器 (spacecraft_id)
├── 分系统 (subsystem_id)
│   ├── 单机 (single_id + subsystem_id)
│   │   └── 模块 (module_id + single_id + subsystem_id)
```

- **分系统绑定**：只设置 `subsystem_id`
- **单机绑定**：设置 `single_id` 和 `subsystem_id`
- **模块绑定**：设置 `module_id`、`single_id` 和 `subsystem_id`

### 3. 数据示例

假设原始遥测代号：
```sql
id=1, name='YG30_PWR_V', description='整星母线电压', spacecraft_id=26
```

绑定到分系统4后：
```sql
id=10, name='YG30_PWR_V', description='整星母线电压', 
spacecraft_id=26, subsystem_id=4
```

绑定到单机5后：
```sql
id=11, name='YG30_PWR_V', description='整星母线电压', 
spacecraft_id=26, subsystem_id=4, single_id=5
```

绑定到模块6后：
```sql
id=12, name='YG30_PWR_V', description='整星母线电压', 
spacecraft_id=26, subsystem_id=4, single_id=5, module_id=6
```

## 🔧 技术实现

### 1. 后端实现

#### 绑定服务核心代码
```java
// 创建新的绑定记录（复制原遥测代号信息）
TelemetryCode newBinding = new TelemetryCode();
newBinding.setSerialNum(telemetryCode.getSerialNum());
newBinding.setName(telemetryCode.getName());
newBinding.setDescription(telemetryCode.getDescription());
newBinding.setNote(telemetryCode.getNote());
newBinding.setFileName(telemetryCode.getFileName());

// 设置航天器ID
newBinding.setSpacecraftId(getSpacecraftId(bindDto.getLevel(), bindDto.getId()));

// 设置层级ID并保持层级关系
switch (bindDto.getLevel()) {
    case "subsystem":
        newBinding.setSubsystemId(bindDto.getId());
        break;
    case "single":
        newBinding.setSingleId(bindDto.getId());
        newBinding.setSubsystemId(getSubsystemIdForSingle(bindDto.getId()));
        break;
    case "module":
        newBinding.setModuleId(bindDto.getId());
        Long singleId = getSingleIdForModule(bindDto.getId());
        newBinding.setSingleId(singleId);
        newBinding.setSubsystemId(getSubsystemIdForSingle(singleId));
        break;
}
```

#### 新增API接口
- `DELETE /single/telemetry/unbind/{telemetryCodeId}` - 删除特定绑定记录

### 2. 前端实现

#### 绑定请求
```javascript
const bindData = {
  level: bindDialog.level,
  id: bindDialog.id,
  telemetryCodeId: code.id  // 使用原始遥测代号ID
}
```

#### 删除特定绑定
```javascript
// 删除特定的遥测代号绑定记录
const response = await unbindSpecificTelemetryCode(telemetryCode.id)
```

## 🎨 用户体验

### 1. 绑定操作
- 用户选择要绑定的对象（分系统/单机/模块）
- 点击"绑定遥测代号"按钮
- 在对话框中选择遥测代号（显示原始遥测代号列表）
- 确认绑定后，系统创建新的绑定记录

### 2. 查看绑定
- 点击不同层级的节点，显示该层级绑定的遥测代号
- 同一个遥测代号可以在多个层级中显示
- 每个绑定记录都有独立的ID

### 3. 删除绑定
- 选择特定的遥测代号绑定记录
- 点击删除按钮，只删除该绑定记录
- 不影响其他对象对同一遥测代号的绑定

## 🔍 优势与特点

### 1. 优势
- ✅ **不改变数据库结构**：利用现有表结构实现多对象绑定
- ✅ **数据完整性**：每个绑定都有完整的遥测代号信息
- ✅ **层级关系清晰**：保持完整的层级关系链
- ✅ **独立管理**：每个绑定可以独立删除和管理
- ✅ **查询效率**：可以快速查询特定层级的绑定

### 2. 特点
- 同一遥测代号可以绑定到多个对象
- 每个绑定关系都是独立的记录
- 支持精确的绑定删除操作
- 保持层级继承规则的完整性

## 📊 数据查询示例

### 查询分系统绑定的遥测代号
```sql
SELECT * FROM telemetry_code WHERE subsystem_id = 4;
```

### 查询单机绑定的遥测代号
```sql
SELECT * FROM telemetry_code WHERE single_id = 5;
```

### 查询模块绑定的遥测代号
```sql
SELECT * FROM telemetry_code WHERE module_id = 6;
```

### 查询某遥测代号的所有绑定
```sql
SELECT * FROM telemetry_code WHERE name = 'YG30_PWR_V' AND spacecraft_id = 26;
```

## 🚀 测试验证

### 1. 绑定测试
1. 将同一遥测代号绑定到多个分系统
2. 将同一遥测代号绑定到多个单机
3. 将同一遥测代号绑定到多个模块
4. 验证每个绑定都创建了独立记录

### 2. 查询测试
1. 点击不同层级节点，验证显示正确的绑定数据
2. 验证同一遥测代号在多个层级中都能正确显示

### 3. 删除测试
1. 删除特定绑定记录
2. 验证只删除了选中的绑定，其他绑定不受影响
3. 验证数据显示正确更新

## 🎉 总结

这个实现方案在不改变数据库结构的前提下，成功实现了多对象绑定同一遥测代号的需求：

- **技术可行**：利用现有表结构，通过记录复制实现多绑定
- **功能完整**：支持绑定、查询、删除等完整操作
- **用户友好**：提供直观的操作界面和反馈
- **数据安全**：每个绑定独立管理，删除操作精确可控

该方案为航天器信息管理系统提供了灵活的遥测代号绑定解决方案，满足了复杂的业务需求。

---

*实现完成时间：2025-07-28*  
*方案状态：已实现并测试*
