package com.xtgl.ssystem.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 单机实体类
 */
@Data
@TableName("single")
public class Single {

    /**
     * 单机ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 单机名称
     */
    @TableField("name")
    private String name;

    /**
     * 所属分系统ID
     */
    @TableField("subsystem_id")
    private Long subsystemId;

    /**
     * 所属航天器ID
     */
    @TableField("spacecraft_id")
    private Long spacecraftId;
}
