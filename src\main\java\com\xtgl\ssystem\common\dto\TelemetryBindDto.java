package com.xtgl.ssystem.common.dto;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 遥测代号绑定请求DTO
 */
@Data
public class TelemetryBindDto {

    /**
     * 绑定对象的层级
     * satellite|subsystem|single|module
     */
    @NotBlank(message = "层级不能为空")
    private String level;

    /**
     * 对应层级的ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 要绑定的遥测代号ID
     */
    @NotNull(message = "遥测代号ID不能为空")
    private Long telemetryCodeId;
}
