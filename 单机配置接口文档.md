# 单机配置接口文档

## 概述

本文档描述了单机配置模块的REST API接口，主要提供航天器信息搜索功能。该模块通过内部调用SatelliteController的接口来确保数据一致性。

**基础URL**: `http://localhost:8080`  
**接口前缀**: `/single`

---

## 1. 航天器信息搜索

### 1.1 接口描述

在"航天器信息展示"基础上增加多条件过滤能力，内部转发至 `/satellite/search` 完成检索。支持型号、名称、负责人、研制单位四个维度的模糊/精确查询，查询结果保持相同的分页结构。若所有条件为空，则退化为"展示全部"。

### 1.2 接口信息

**接口地址**: `GET /single/craftsearch`

**请求参数**:

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| model | string | 否 | 型号，可精确匹配 |
| name | string | 否 | 名称，模糊匹配 |
| header | string | 否 | 负责人，模糊匹配 |
| company | string | 否 | 研制单位，模糊匹配 |
| pageNo | int | 否 | 页码，默认 1 |
| pageSize | int | 否 | 每页条数，默认 10 |

### 1.3 请求示例

```http
GET /single/craftsearch?model=HY-2&name=HY-2D&header=张三&company=中国空间技术研究院&pageNo=1&pageSize=10
```

```http
GET /single/craftsearch?name=长征&pageNo=1&pageSize=20
```

```http
GET /single/craftsearch?pageNo=1&pageSize=10
```

### 1.4 成功响应

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 57,
    "list": [
      {
        "id": 7,
        "name": "HY-2D",
        "model": "HY-2",
        "header": "张三",
        "createTime": "2025-07-28",
        "company": "中国空间技术研究院",
        "receiveTime": "2025-07-28T10:30:00",
        "status": "未开始",
        "batch": 2024,
        "mqttName": "emqx_prod"
      }
    ],
    "page": 1,
    "size": 10
  }
}
```

**响应字段说明**:

| 字段 | 类型 | 说明 |
|------|------|------|
| code | int | 响应状态码，200表示成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| data.total | long | 总记录数 |
| data.list | array | 数据列表 |
| data.page | int | 当前页码 |
| data.size | int | 每页条数 |

**航天器信息字段说明**:

| 字段 | 类型 | 说明 |
|------|------|------|
| id | long | 航天器ID |
| name | string | 航天器名称 |
| model | string | 航天器型号 |
| header | string | 负责人 |
| createTime | string | 创建时间（日期格式：YYYY-MM-DD） |
| company | string | 所属单位 |
| receiveTime | string | 开始接收时间（ISO格式：YYYY-MM-DDTHH:mm:ss） |
| status | string | 卫星状态 |
| batch | int | 批次 |
| mqttName | string | MQTT连接名称 |

### 1.5 错误响应

当接口调用出现错误时，会返回以下格式的错误响应：

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null
}
```

**常见错误码**:

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

---

## 2. 技术实现说明

### 2.1 数据一致性保证

- 接口不直接访问卫星表，而是通过内部调用 `SatelliteController` 的 `/satellite/search` 接口
- 确保与航天器管理模块的数据完全一致
- 若 `SatelliteController` 接口升级（新增字段或校验规则），`SingleController` 会自动透传，无需同步修改

### 2.2 查询逻辑

- **精确匹配**：型号字段支持精确匹配
- **模糊匹配**：名称、负责人、研制单位支持模糊查询
- **全量查询**：当所有查询条件为空时，返回所有航天器信息
- **分页支持**：支持标准的分页查询，默认每页10条记录

### 2.3 响应格式转换

接口将 `SatelliteController` 返回的 `PageResult` 格式转换为符合单机配置模块规范的响应格式：

- `PageResult.getTotal()` → `data.total`
- `PageResult.getList()` → `data.list`  
- 请求参数 `pageNo` → `data.page`
- 请求参数 `pageSize` → `data.size`

---

## 3. 前端集成说明

### 3.1 页面功能

- **搜索区域**：提供型号、名称、负责人、所属单位四个搜索条件
- **数据表格**：展示航天器信息，包含遥测代号查看和配置操作
- **分页组件**：支持分页浏览和每页条数设置

### 3.2 操作按钮

- **搜索按钮**：执行条件查询
- **重置按钮**：清空所有搜索条件并重新查询
- **查看按钮**：查看航天器遥测代号（功能待开发）
- **配置按钮**：进入单机配置页面（功能待开发）

---

## 4. 使用示例

### 4.1 查询所有航天器

```javascript
// 前端调用示例
import { craftSearch } from '@/api/single'

const searchAllCrafts = async () => {
  const response = await craftSearch({
    pageNo: 1,
    pageSize: 10
  })
  console.log(response.data.list)
}
```

### 4.2 按条件查询

```javascript
// 按型号和名称查询
const searchByCondition = async () => {
  const response = await craftSearch({
    model: 'HY-2',
    name: 'HY-2D',
    pageNo: 1,
    pageSize: 20
  })
  console.log(response.data)
}
```

### 4.3 模糊查询

```javascript
// 按负责人模糊查询
const searchByHeader = async () => {
  const response = await craftSearch({
    header: '张',  // 会匹配所有包含"张"的负责人
    pageNo: 1,
    pageSize: 10
  })
  console.log(response.data.list)
}
```

---

## 5. 注意事项

1. **参数验证**：所有查询参数都是可选的，但建议前端进行基本的参数验证
2. **分页限制**：建议每页条数不超过100条，以保证查询性能
3. **模糊查询**：名称、负责人、研制单位字段支持模糊匹配，会自动添加通配符
4. **数据同步**：数据与航天器管理模块完全同步，无需担心数据不一致问题

---

*文档生成时间：2025-07-28*  
*API版本：v1.0*
