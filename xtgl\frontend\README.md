# 系统管理平台前端

## 项目介绍
系统管理平台前端，基于Vue 3 + Vite + Element Plus开发。

## 功能模块
- 航天器管理
  - 航天器列表
  - 单机配置
  - 算法管理
- 权限管理
  - 用户管理
  - 角色管理
- 系统管理
  - MQTT管理
  - 操作日志

## 开发环境
- Node.js 16+
- Vue 3
- Vite 4
- Element Plus 2.3+

## 项目启动
```bash
# 安装依赖
npm install

# 开发环境启动
npm run dev

# 构建生产环境
npm run build
```

## 项目结构
```
frontend/
├── public/                # 静态资源
├── src/
│   ├── api/               # API接口
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   ├── router/            # 路由配置
│   ├── store/             # 状态管理
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── index.html             # HTML模板
├── vite.config.js         # Vite配置
└── package.json           # 项目依赖
``` 