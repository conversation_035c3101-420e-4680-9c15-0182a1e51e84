# 遥测代号绑定API测试示例

## 测试环境
- 后端服务：http://localhost:8080
- 前端服务：http://localhost:5175

## API测试用例

### 1. 查询卫星遥测代号
```bash
curl -X GET "http://localhost:8080/single/codesearch?satelliteId=26"
```

### 2. 绑定遥测代号到分系统
```bash
curl -X POST "http://localhost:8080/single/telemetry/bind" \
  -H "Content-Type: application/json" \
  -d '{
    "level": "subsystem",
    "id": 4,
    "telemetryCodeId": 7
  }'
```

### 3. 查询分系统绑定的遥测代号
```bash
curl -X GET "http://localhost:8080/single/telemetry/bound?level=subsystem&id=4"
```

### 4. 查询可绑定的遥测代号
```bash
curl -X GET "http://localhost:8080/single/telemetry/available?level=subsystem&id=4"
```

### 5. 删除遥测代号绑定
```bash
curl -X DELETE "http://localhost:8080/single/telemetry/unbind?level=subsystem&id=4"
```

## 前端测试步骤

1. 访问 http://localhost:5175
2. 点击"单机配置"菜单
3. 选择一个航天器，点击"配置"按钮
4. 在配置页面：
   - 查看右侧遥测代号表格
   - 点击左侧树节点查看不同层级的遥测代号
   - 选择分系统/单机/模块节点，点击"绑定遥测代号"
   - 在弹出对话框中选择遥测代号并确认绑定
   - 选择遥测代号行，点击"删除"按钮删除绑定

## 预期结果

- 绑定成功：显示成功提示，数据更新
- 层级继承违规：显示"父级未绑定遥测代号 XXX"错误
- 重复绑定：显示"该对象已绑定遥测代号 XXX"错误
- 删除成功：显示成功提示，数据更新

## 数据库验证

可以通过查询数据库验证绑定结果：

```sql
-- 查看遥测代号绑定情况
SELECT id, name, spacecraft_id, subsystem_id, single_id, module_id 
FROM telemetry_code 
WHERE spacecraft_id = 26;

-- 查看分系统绑定的遥测代号
SELECT * FROM telemetry_code WHERE subsystem_id = 4;

-- 查看单机绑定的遥测代号
SELECT * FROM telemetry_code WHERE single_id IS NOT NULL;

-- 查看模块绑定的遥测代号
SELECT * FROM telemetry_code WHERE module_id IS NOT NULL;
```
