<template>
  <div class="upload-container">
    <!-- 面包屑导航 -->
    <div class="breadcrumb">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/spacecraft/list' }">
          <el-icon><HomeFilled /></el-icon>航天器列表
        </el-breadcrumb-item>
        <el-breadcrumb-item>{{ route.query.isNew === 'false' ? '编辑航天器' : '新建航天器' }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    
    <!-- 文件上传区域 -->
    <div class="section-title">
      <div class="divider"></div>
      <h3>文件上传</h3>
    </div>

    <div class="file-upload-area">
      <!-- 已绑定数据的提示信息（导入模式） -->
      <div v-if="isImportMode && sessionUploadState?.isBoundFile" class="binding-info">
        <el-alert
          title="航天器已绑定数据"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>当前航天器已绑定文件：<strong>{{ fileList[0]?.name }}</strong></p>
            <p>数据统计：{{ sessionUploadState.success || 0 }} 条遥测数据</p>
            <p class="edit-tip">
              <el-icon><InfoFilled /></el-icon>
              如需修改绑定数据，请前往
              <el-button type="text" @click="goToEditPage">编辑页面</el-button>
              重新上传文件
            </p>
            <p class="warning-text">
              <el-icon><WarningFilled /></el-icon>
              导入模式下无法修改已绑定的数据，请使用编辑功能
            </p>
          </template>
        </el-alert>
      </div>

      <!-- 文件绑定提示信息（编辑模式） -->
      <div v-else-if="fileList.length > 0 && sessionUploadState" class="binding-info">
        <el-alert
          title="文件已绑定"
          type="success"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>已绑定文件：<strong>{{ fileList[0]?.name }}</strong></p>
            <p>数据统计：成功 {{ sessionUploadState.success || 0 }} 条
              <span v-if="sessionUploadState.failed > 0">，失败 {{ sessionUploadState.failed }} 条</span>
            </p>
            <p class="edit-tip">
              <el-icon><InfoFilled /></el-icon>
              如需修改绑定数据，可以重新上传文件覆盖当前数据
            </p>
          </template>
        </el-alert>
      </div>

      <div class="file-info">
        <span>文件名称：</span>
        <div class="file-display">
          <template v-if="fileList.length > 0">
            <div class="file-item">
              <el-tag v-for="file in fileList" :key="file.name" class="file-tag">
                {{ file.name }}
                <el-icon class="el-icon--right" @click="handleRemoveFile(file)">
                  <Close />
                </el-icon>
              </el-tag>
              <div v-if="sessionUploadState" class="upload-info">
                <span class="info-item">上传时间: {{ formatUploadTime(sessionUploadState.uploadTime) }}</span>
                <span class="info-item">成功: {{ sessionUploadState.success || 0 }} 条</span>
                <span v-if="sessionUploadState.failed > 0" class="info-item error">失败: {{ sessionUploadState.failed }} 条</span>
              </div>
            </div>
          </template>
          <span v-else class="no-file">未上传文件</span>
        </div>
      </div>
      
      <el-upload
        ref="uploadRef"
        class="upload-component"
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        :limit="1"
        accept=".xlsx"
        :file-list="fileList"
      >
        <el-button type="primary">上传文件</el-button>
        <template #tip>
          <div class="el-upload__tip">只能上传 xlsx 格式文件</div>
        </template>
      </el-upload>
    </div>
    
    <!-- 遥测代号数据 -->
    <div class="section-title">
      <div class="divider"></div>
      <h3>航天器遥测代号</h3>
    </div>
    
    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%"
      height="calc(100vh - 450px)"
    >
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="code" label="遥测代号" min-width="120" />
      <el-table-column prop="description" label="代号描述" min-width="200" />
      <el-table-column prop="remark" label="备注" min-width="150" />
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.pageNo"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>
    
    <!-- 底部按钮 -->
    <div class="button-container">
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="showPrevButton" type="primary" @click="handlePrev">上一步</el-button>
      <el-button
        v-if="!isImportModeWithBoundData"
        type="primary"
        @click="handleComplete"
      >
        {{ getCompleteButtonText() }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { HomeFilled, Close, InfoFilled, WarningFilled } from '@element-plus/icons-vue'
import { uploadTelemetryBatch, getTelemetryByFileName, bindTelemetryToSatellite, deleteTelemetryByFileName, createSatellite, getBoundFileInfo, deleteBoundTelemetryData } from '@/api/satellite'

const router = useRouter()
const route = useRoute()
const uploadRef = ref(null)
const loading = ref(false)
const satelliteId = route.params.id
const satelliteName = route.query.name || '未命名航天器'
const satelliteModel = route.query.model || '未知型号'
const fileList = ref([])
const tableData = ref([])

// 分页配置
const pagination = reactive({
  pageNo: 1,
  pageSize: 10,
  total: 0
})

// 上传的文件名（批次标识）
const uploadFileName = ref(null)
// 当前会话的上传状态
const sessionUploadState = ref(null)

// 判断是否显示上一步按钮（从创建流程进入才显示）
const showPrevButton = ref(route.query.isNew !== undefined)

// 判断是否为导入模式（没有isNew参数且不是new航天器）
const isImportMode = ref(route.query.isNew === undefined && satelliteId !== 'new')

// 判断是否为导入模式且已绑定数据
const isImportModeWithBoundData = computed(() => {
  return isImportMode.value && sessionUploadState.value?.isBoundFile
})

// 获取完成按钮文字
const getCompleteButtonText = () => {
  if (route.query.isNew === 'false') {
    return '编辑完成'
  } else if (satelliteId === 'new') {
    return '创建完成'
  } else {
    return '导入完成'
  }
}

// 格式化上传时间
const formatUploadTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 页面初始化
onMounted(async () => {
  // 检查是否有会话状态（从localStorage恢复）
  const sessionKey = `upload_session_${satelliteId}`
  const savedState = localStorage.getItem(sessionKey)

  if (savedState) {
    try {
      sessionUploadState.value = JSON.parse(savedState)
      uploadFileName.value = sessionUploadState.value.fileName

      // 恢复文件显示
      if (sessionUploadState.value.originalFileName) {
        fileList.value = [{
          name: sessionUploadState.value.originalFileName,
          status: 'success'
        }]
      }
    } catch (error) {
      console.error('恢复会话状态失败', error)
    }
  } else if (satelliteId && satelliteId !== 'new') {
    // 如果没有会话状态且是已存在的航天器，检查是否有已绑定的文件
    await checkBoundFile()
  }

  // 加载遥测代号数据
  displayUploadedData()
})

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  displayUploadedData()
}

// 分页页码变化
const handleCurrentChange = (val) => {
  pagination.pageNo = val
  displayUploadedData()
}

// 处理文件变化
const handleFileChange = (file) => {
  if (file.raw.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    ElMessage.error('只能上传 xlsx 格式文件!')
    fileList.value = []
    return false
  }
  
  fileList.value = [file]
  
  // 自动上传文件
  uploadFile(file)
  
  return false
}

// 移除文件
const handleRemoveFile = async () => {
  // 检查是否是已绑定的文件
  if (sessionUploadState.value?.isBoundFile) {
    // 已绑定的文件需要确认删除
    try {
      await ElMessageBox.confirm(
        '这将删除航天器已绑定的所有遥测数据，操作不可恢复。是否继续？',
        '确认删除已绑定数据',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      // 用户确认删除，调用删除已绑定数据的API
      try {
        const res = await deleteBoundTelemetryData(satelliteId)
        ElMessage.success(res.data.message || '已绑定数据删除成功')
      } catch (error) {
        console.error('删除已绑定数据失败', error)
        ElMessage.error('删除已绑定数据失败：' + (error.response?.data?.message || error.message))
        return
      }
    } catch {
      // 用户取消删除
      return
    }
  } else {
    // 临时文件，正常删除
    try {
      if (uploadFileName.value) {
        await deleteTelemetryByFileName(uploadFileName.value)
        ElMessage.success('临时数据已删除')
      }
    } catch (error) {
      console.error('删除临时数据失败', error)
      ElMessage.warning('删除临时数据失败，但文件已移除')
    }
  }

  // 清除会话状态
  localStorage.removeItem(`upload_session_${satelliteId}`)

  fileList.value = []
  tableData.value = []
  pagination.total = 0
  uploadFileName.value = null
  sessionUploadState.value = null
}

// 上传文件
const uploadFile = async (file) => {
  loading.value = true
  try {
    // 生成唯一的文件名作为批次标识
    const timestamp = new Date().getTime()
    const fileName = `upload_${timestamp}_${Math.random().toString(36).substring(2, 11)}`

    const res = await uploadTelemetryBatch(file.raw, fileName)
    uploadFileName.value = res.data.fileName || fileName

    // 保存会话状态
    const sessionState = {
      fileName: uploadFileName.value,
      originalFileName: file.name,
      uploadTime: new Date().toISOString(),
      success: res.data.success,
      failed: res.data.failed
    }
    sessionUploadState.value = sessionState
    localStorage.setItem(`upload_session_${satelliteId}`, JSON.stringify(sessionState))

    ElMessage.success(`文件上传成功，成功处理 ${res.data.success || 0} 条数据`)

    // 如果有失败的数据，提示用户
    if (res.data.failed && res.data.failed > 0) {
      ElMessage.warning(`有 ${res.data.failed} 条数据处理失败`)
    }

    // 显示上传的数据
    await displayUploadedData()
  } catch (error) {
    console.error('文件上传失败', error)
    ElMessage.error('文件上传失败')
    fileList.value = []
  } finally {
    loading.value = false
  }
}

// 显示上传的数据
const displayUploadedData = async () => {
  loading.value = true
  try {
    let telemetryData = []

    if (uploadFileName.value) {
      // 如果有上传的文件，显示临时数据
      const res = await getTelemetryByFileName(uploadFileName.value)
      telemetryData = res.data || []
    } else if (satelliteId && satelliteId !== 'new') {
      // 如果是已存在的航天器，显示已绑定的数据
      const { getTelemetryData } = await import('@/api/satellite')
      const res = await getTelemetryData({ satelliteId })
      telemetryData = res.data || []
    }

    // 计算分页数据
    const startIndex = (pagination.pageNo - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize

    tableData.value = telemetryData.slice(startIndex, endIndex).map(item => ({
      id: item.id,
      code: item.name || item.code,
      description: item.description,
      remark: item.note || ''
    }))

    pagination.total = telemetryData.length
  } catch (error) {
    console.error('获取遥测代号数据失败', error)
    ElMessage.error('获取遥测代号数据失败')
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = async () => {
  try {
    // 如果有上传的临时数据，删除它
    if (uploadFileName.value) {
      await deleteTelemetryByFileName(uploadFileName.value)
    }
  } catch (error) {
    console.error('删除临时数据失败', error)
  }

  // 清除会话状态
  localStorage.removeItem(`upload_session_${satelliteId}`)

  router.push('/spacecraft/list')
}

// 处理上一步
const handlePrev = () => {
  router.push({
    path: '/spacecraft/create',
    query: {
      id: satelliteId,
      name: route.query.name,
      model: route.query.model,
      company: route.query.company,
      header: route.query.header,
      batch: route.query.batch,
      mqttName: route.query.mqttName
    }
  })
}

// 跳转到编辑页面
const goToEditPage = () => {
  router.push({
    path: '/spacecraft/create',
    query: {
      id: satelliteId,
      name: route.query.name,
      model: route.query.model,
      company: route.query.company,
      header: route.query.header,
      batch: route.query.batch,
      mqttName: route.query.mqttName
    }
  })
}

// 检查已绑定的文件
const checkBoundFile = async () => {
  try {
    const res = await getBoundFileInfo(satelliteId)
    const fileInfo = res.data

    if (fileInfo.hasBoundFile) {
      // 有已绑定的文件，显示文件信息
      fileList.value = [{
        name: fileInfo.fileName || '已绑定文件',
        status: 'success'
      }]

      // 设置会话状态以显示文件信息
      sessionUploadState.value = {
        fileName: fileInfo.fileName,
        originalFileName: fileInfo.fileName,
        uploadTime: new Date().toISOString(),
        success: fileInfo.totalRecords || 0,
        failed: 0,
        isBoundFile: true // 标记这是已绑定的文件
      }
    }
  } catch (error) {
    console.error('检查已绑定文件失败', error)
    // 不显示错误信息，因为这不是关键功能
  }
}

// 处理创建完成
const handleComplete = async () => {
  if (fileList.value.length > 0 && !uploadFileName.value) {
    ElMessage.warning('文件正在上传中，请稍候...')
    return
  }

  try {
    let finalSatelliteId = satelliteId

    // 如果是新建航天器，先创建航天器
    if (satelliteId === 'new') {
      const satelliteData = {
        model: route.query.model,
        name: route.query.name,
        company: route.query.company,
        header: route.query.header,
        batch: Number(route.query.batch),
        mqttName: route.query.mqttName
      }

      const createRes = await createSatellite(satelliteData)
      finalSatelliteId = createRes.data.id
      ElMessage.success('航天器创建成功')
    }

    // 如果有上传的临时数据，绑定到航天器
    if (uploadFileName.value) {
      await bindTelemetryToSatellite(finalSatelliteId, uploadFileName.value)
      ElMessage.success('遥测代号数据绑定成功')
    }

    // 清除会话状态
    localStorage.removeItem(`upload_session_${satelliteId}`)

    router.push('/spacecraft/list')
  } catch (error) {
    console.error('创建航天器或绑定数据失败', error)
    ElMessage.error('操作失败：' + (error.response?.data?.message || error.message))
  }
}
</script>

<style scoped>
.upload-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  height: 100%;
}

.breadcrumb {
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  margin: 20px 0;
}

.section-title .divider {
  width: 4px;
  height: 16px;
  background-color: #409EFF;
  margin-right: 8px;
  border-radius: 2px;
}

.section-title h3 {
  font-size: 16px;
  margin: 0;
  font-weight: bold;
}

.file-upload-area {
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
}

.binding-info {
  margin-bottom: 20px;
}

.edit-tip {
  margin: 8px 0 0 0;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
}

.edit-tip .el-icon {
  color: #409EFF;
}

.warning-text {
  margin: 8px 0 0 0;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 13px;
  color: #E6A23C;
  font-weight: 500;
}

.warning-text .el-icon {
  color: #E6A23C;
}

.file-info {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.file-info span {
  white-space: nowrap;
  margin-right: 5px;
}

.file-display {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-tag {
  display: flex;
  align-items: center;
}

.upload-info {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.info-item {
  padding: 2px 6px;
  background: #f0f2f5;
  border-radius: 3px;
}

.info-item.error {
  background: #fef0f0;
  color: #f56c6c;
}

.no-file {
  color: #999;
}

.upload-component {
  margin-top: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  background-color: #f5f7fa;
  padding: 10px 0;
  border-radius: 4px;
}

.button-container {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

:deep(.el-tag) {
  display: flex;
  align-items: center;
}

:deep(.el-tag .el-icon) {
  margin-left: 5px;
  cursor: pointer;
}
</style> 