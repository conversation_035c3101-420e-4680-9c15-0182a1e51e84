create table telemetry_code
(
    id                  bigint auto_increment comment '主键'    primary key,
    serial_num          int          not null comment '序号',
    name                varchar(64)  not null comment '代号名称',
    description         varchar(255) null comment '代号描述',
    note                varchar(255) null comment '备注',
    subsystem_id        bigint       null comment '所属卫星id',
    spacecraft_id       bigint       null comment '所属分系统id',
    single_id           bigint       null comment '所属单机id',
    module_id           bigint       null comment '所属模块id',
    file_name           varchar(255) null comment '所属文件名称'
)
    comment '遥测代号表';

