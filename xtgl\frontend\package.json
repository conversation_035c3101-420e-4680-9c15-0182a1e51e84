{"name": "xtgl-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.5.0", "element-plus": "^2.3.12", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-router": "^4.2.4", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "sass": "^1.66.1", "vite": "^4.4.9"}}