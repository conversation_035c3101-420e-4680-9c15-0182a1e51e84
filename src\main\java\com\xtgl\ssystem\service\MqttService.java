package com.xtgl.ssystem.service;

import com.xtgl.ssystem.common.entity.PageResult;
import com.xtgl.ssystem.util.MqttClientUtil;
import com.xtgl.ssystem.common.dto.MqttDto;
import com.xtgl.ssystem.common.dto.MqttTestResultDto;

import java.util.List;

/**
 * MQTT服务接口
 */
public interface MqttService {

    /**
     * 新建MQTT连接配置
     *
     * @param mqttDto MQTT配置DTO
     * @return 新建的MQTT配置DTO，包含ID
     */
    MqttDto createMqtt(MqttDto mqttDto);

    /**
     * 测试MQTT连接
     *
     * @param mqttDto MQTT配置DTO
     * @return 连接测试结果
     */
    MqttTestResultDto testMqttConnection(MqttDto mqttDto);

    /**
     * 更新MQTT连接配置
     *
     * @param mqttDto MQTT配置DTO
     * @return 更新后的MQTT配置DTO
     */
    MqttDto updateMqtt(MqttDto mqttDto);

    /**
     * 删除MQTT连接配置
     *
     * @param id 配置ID
     * @return 是否删除成功
     */
    boolean deleteMqtt(Long id);

    /**
     * 根据ID获取MQTT连接配置
     *
     * @param id 配置ID
     * @return MQTT配置DTO
     */
    MqttDto getMqttById(Long id);
    
    /**
     * 根据名称获取MQTT连接配置
     *
     * @param name 名称
     * @return MQTT配置DTO
     */
    MqttDto getMqttByName(String name);

    /**
     * 分页查询MQTT连接配置
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param status   状态筛选，可为null表示查询全部
     * @return 分页结果
     */
    PageResult<MqttDto> pageMqtt(Integer pageNum, Integer pageSize, Boolean status);
    
    /**
     * 获取所有MQTT连接名称
     *
     * @return MQTT连接名称列表
     */
    List<String> getAllMqttNames();
} 