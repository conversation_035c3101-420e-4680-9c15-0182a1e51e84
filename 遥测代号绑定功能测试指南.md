# 遥测代号绑定功能测试指南

## 🎯 功能概述

本功能实现了单机配置模块的遥测代号绑定功能，支持为卫星、分系统、单机、模块绑定遥测代号，并遵守层级继承约束。

## 🔧 实现的功能

### 后端API接口

1. **绑定遥测代号**: `POST /single/telemetry/bind`
2. **查询卫星遥测代号**: `GET /single/codesearch`
3. **查询绑定的遥测代号**: `GET /single/telemetry/bound`
4. **查询可绑定的遥测代号**: `GET /single/telemetry/available`
5. **删除遥测代号绑定**: `DELETE /single/telemetry/unbind`

### 前端功能

1. **遥测代号展示**: 右侧表格展示遥测代号数据
2. **绑定遥测代号**: 点击"绑定遥测代号"按钮弹出对话框
3. **删除遥测代号**: 选中遥测代号后点击删除按钮
4. **层级继承规则**: 子级只能绑定父级已绑定的遥测代号

## 📱 测试步骤

### 前置条件

1. 确保后端服务运行在 http://localhost:8080
2. 确保前端服务运行在 http://localhost:5175
3. 数据库中需要有航天器数据和遥测代号数据

### 步骤1：进入配置页面

1. 打开浏览器访问 http://localhost:5175
2. 点击左侧菜单"单机配置"
3. 在航天器列表中找到一个航天器
4. 点击该航天器行的"配置"按钮
5. 进入配置遥测代号页面

### 步骤2：查看遥测代号数据

1. 页面右侧应该显示该航天器的遥测代号表格
2. 表格包含：序号、遥测代号、代号描述、备注等列
3. 点击表格中的任意一行，该行应该高亮显示
4. 选中遥测代号行后，删除按钮应该变为可用状态

### 步骤3：测试层级展示

1. 点击左侧树形组件中的不同节点：
   - 点击航天器节点：显示该航天器的所有遥测代号
   - 点击分系统节点：显示该分系统绑定的遥测代号
   - 点击单机节点：显示该单机绑定的遥测代号
   - 点击模块节点：显示该模块绑定的遥测代号

### 步骤4：测试绑定功能

1. 在左侧树形组件中选择一个分系统、单机或模块节点
2. 点击"绑定遥测代号"按钮
3. 应该弹出绑定对话框，包含：
   - 绑定对象信息（名称和层级）
   - 可绑定的遥测代号表格
   - 确定和取消按钮
4. 在表格中点击选择要绑定的遥测代号（可多选）
5. 选中的行应该高亮显示
6. 点击"确定"按钮执行绑定
7. 绑定成功后应该显示成功提示，对话框关闭
8. 右侧遥测代号表格应该更新显示新绑定的数据

### 步骤5：测试层级继承规则

1. 尝试为分系统绑定航天器未绑定的遥测代号
   - 应该显示错误提示："父级未绑定遥测代号 XXX"
2. 尝试为单机绑定分系统未绑定的遥测代号
   - 应该显示错误提示："父级未绑定遥测代号 XXX"
3. 尝试为模块绑定单机未绑定的遥测代号
   - 应该显示错误提示："父级未绑定遥测代号 XXX"

### 步骤6：测试删除功能

1. 在右侧遥测代号表格中点击选择一行
2. 点击"删除"按钮
3. 应该弹出确认对话框
4. 点击"确定删除"执行删除
5. 删除成功后应该显示成功提示
6. 表格数据应该更新，被删除的遥测代号不再显示

### 步骤7：测试按钮状态

1. **绑定遥测代号按钮**：
   - 未选中节点时：禁用
   - 选中航天器节点时：禁用
   - 选中其他节点时：启用

2. **删除按钮**：
   - 未选中任何内容时：禁用
   - 选中遥测代号行时：启用
   - 选中树节点（非航天器）时：启用

## 🔍 预期结果

### 正常情况

1. **绑定成功**：显示"遥测代号绑定成功"提示
2. **删除成功**：显示"遥测代号删除成功"提示
3. **数据更新**：操作后表格数据自动刷新
4. **层级展示**：点击不同节点显示对应的遥测代号

### 错误情况

1. **层级继承违规**：显示"父级未绑定遥测代号 XXX"
2. **重复绑定**：显示"该对象已绑定遥测代号 XXX"
3. **节点不存在**：显示相应的错误提示
4. **网络错误**：显示网络错误提示

## 🐛 常见问题

### 问题1：绑定对话框显示空数据
**可能原因**：父级没有绑定任何遥测代号
**解决方案**：先为父级绑定遥测代号

### 问题2：删除按钮不可用
**可能原因**：没有选中遥测代号行或树节点
**解决方案**：点击选择要删除的内容

### 问题3：绑定失败
**可能原因**：违反层级继承规则
**解决方案**：确保父级已绑定相同的遥测代号

## 📊 测试数据要求

为了完整测试功能，需要确保数据库中有：

1. **航天器数据**：至少一个航天器记录
2. **层级结构**：航天器下有分系统、单机、模块的完整层级
3. **遥测代号数据**：航天器关联的遥测代号数据

## 🎉 测试完成标准

- ✅ 所有按钮状态正确
- ✅ 绑定功能正常工作
- ✅ 删除功能正常工作
- ✅ 层级继承规则生效
- ✅ 错误处理正确
- ✅ 用户界面友好

---

*测试指南创建时间：2025-07-28*  
*功能状态：已实现*
