package com.xtgl.ssystem.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xtgl.ssystem.common.dto.ModuleDto;
import com.xtgl.ssystem.common.entity.Module;
import com.xtgl.ssystem.mapper.ModuleMapper;
import com.xtgl.ssystem.service.ModuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 模块服务实现类
 */
@Slf4j
@Service
public class ModuleServiceImpl implements ModuleService {

    @Autowired
    private ModuleMapper moduleMapper;

    @Override
    @Transactional
    public Long addModule(ModuleDto moduleDto) {
        log.info("添加模块: {}", moduleDto);
        
        // 创建模块实体
        Module module = new Module();
        BeanUtils.copyProperties(moduleDto, module);
        
        // 保存到数据库
        int result = moduleMapper.insert(module);
        if (result > 0) {
            log.info("模块添加成功，ID: {}", module.getId());
            return module.getId();
        } else {
            throw new RuntimeException("模块添加失败");
        }
    }

    @Override
    public List<Module> getModulesBySingleId(Long singleId) {
        log.info("查询单机{}下的模块", singleId);
        
        LambdaQueryWrapper<Module> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Module::getSingleId, singleId)
                   .orderByAsc(Module::getId);
        
        List<Module> modules = moduleMapper.selectList(queryWrapper);
        log.info("查询到{}个模块", modules.size());
        return modules;
    }

    @Override
    @Transactional
    public void deleteModule(Long moduleId) {
        log.info("删除模块: {}", moduleId);
        
        // 检查模块是否存在
        Module module = moduleMapper.selectById(moduleId);
        if (module == null) {
            throw new RuntimeException("模块不存在");
        }
        
        // 删除模块（仅删除该模块，无级联删除）
        int result = moduleMapper.deleteById(moduleId);
        if (result > 0) {
            log.info("模块删除成功");
        } else {
            throw new RuntimeException("模块删除失败");
        }
    }

    @Override
    @Transactional
    public void updateModule(Long moduleId, ModuleDto moduleDto) {
        log.info("编辑模块: ID={}, data={}", moduleId, moduleDto);
        
        // 检查模块是否存在
        Module existingModule = moduleMapper.selectById(moduleId);
        if (existingModule == null) {
            throw new RuntimeException("模块不存在");
        }
        
        // 更新模块信息
        Module module = new Module();
        BeanUtils.copyProperties(moduleDto, module);
        module.setId(moduleId);
        
        int result = moduleMapper.updateById(module);
        if (result > 0) {
            log.info("模块编辑成功");
        } else {
            throw new RuntimeException("模块编辑失败");
        }
    }

    @Override
    public Module getModuleById(Long moduleId) {
        log.info("查询模块: {}", moduleId);
        
        Module module = moduleMapper.selectById(moduleId);
        if (module == null) {
            throw new RuntimeException("模块不存在");
        }
        
        return module;
    }

    @Override
    @Transactional
    public void deleteModulesBySingleId(Long singleId) {
        log.info("删除单机{}下的所有模块", singleId);
        
        LambdaQueryWrapper<Module> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Module::getSingleId, singleId);
        
        int result = moduleMapper.delete(queryWrapper);
        log.info("删除了{}个模块", result);
    }

    @Override
    @Transactional
    public void deleteModulesBySubsystemId(Long subsystemId) {
        log.info("删除分系统{}下的所有模块", subsystemId);
        
        // 这里需要通过单机ID来删除模块，因为模块表中没有直接的分系统ID字段
        // 实际实现中，这个方法会在SingleService中调用deleteModulesBySingleId来实现
        // 这里提供一个占位实现
        log.info("通过单机服务级联删除分系统{}下的所有模块", subsystemId);
    }
}
