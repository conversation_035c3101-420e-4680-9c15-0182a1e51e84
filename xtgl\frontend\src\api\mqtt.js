import request from './request'

// 获取MQTT连接列表
export function getMqttList(params) {
  // 确保参数名称与后端一致
  const queryParams = {
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 10
  }
  
  // 仅在 status 非 null 或 undefined 时添加此参数
  if (params.status !== null && params.status !== undefined) {
    queryParams.status = params.status
  }
  
  console.log('MQTT列表请求参数:', queryParams)
  
  return request({
    url: '/mqtt',
    method: 'get',
    params: queryParams
  })
}

// 获取单个MQTT连接详情
export function getMqttDetail(id) {
  return request({
    url: `/mqtt/${id}`,
    method: 'get'
  })
}

// 创建新的MQTT连接
export function createMqtt(data) {
  // 确保userName字段正确
  if (data.username && !data.userName) {
    data.userName = data.username;
    delete data.username;
  }
  
  return request({
    url: '/mqtt/new',
    method: 'post',
    data
  })
}

// 测试MQTT连接
export function testMqttConnection(data) {
  // 确保userName字段正确
  if (data.username && !data.userName) {
    data.userName = data.username;
    delete data.username;
  }
  
  return request({
    url: '/mqtt/test',
    method: 'post',
    data
  })
}

// 更新MQTT连接
export function updateMqtt(data) {
  // 确保userName字段正确
  if (data.username && !data.userName) {
    data.userName = data.username;
    delete data.username;
  }
  
  // 添加模拟的updatePerson
  if (!data.updatePerson) {
    data.updatePerson = 'admin';
  }
  
  return request({
    url: '/mqtt/update',
    method: 'put',
    data
  })
}

// 删除MQTT连接
export function deleteMqtt(id) {
  return request({
    url: '/mqtt/delete',
    method: 'delete',
    params: { id }
  })
} 