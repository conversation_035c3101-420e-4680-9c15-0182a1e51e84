# 航天器信息管理接口文档

## 概述

本文档描述了航天器信息管理系统的所有REST API接口，包括航天器基础信息管理、遥测代号数据管理、文件上传等功能。

**基础URL**: `http://localhost:8080`  
**接口前缀**: `/satellite`

---

## 1. 航天器基础信息管理

### 1.1 新建航天器

**接口地址**: `POST /satellite/new`

**请求参数**:
```json
{
    "model": "CZ-5B",
    "name": "长征五号B遥三",
    "header": "张三",
    "company": "中国航天科技集团",
    "batch": 1,
    "mqttName": "mqtt_connection_1"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "createTime": "2025-07-28T10:30:00"
    }
}
```

### 1.2 批量删除航天器

**接口地址**: `DELETE /satellite/delete`

**请求参数**:
- `id` (String, 可选): 航天器ID列表，多个ID用英文逗号分隔
- `all` (Boolean, 可选): 是否删除所有，默认false

**请求示例**:
```
DELETE /satellite/delete?id=1,2,3
DELETE /satellite/delete?all=true
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "deleted": 3
    }
}
```

### 1.3 分页条件查询航天器

**接口地址**: `GET /satellite/search`

**请求参数**:
- `model` (String, 可选): 型号（模糊查询）
- `name` (String, 可选): 名称（模糊查询）
- `header` (String, 可选): 负责人（模糊查询）
- `company` (String, 可选): 所属单位（模糊查询）
- `pageNo` (Integer, 可选): 页码，默认1
- `pageSize` (Integer, 可选): 每页条数，默认10

**请求示例**:
```
GET /satellite/search?model=CZ&name=长征&pageNo=1&pageSize=10
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "records": [
            {
                "id": 1,
                "model": "CZ-5B",
                "name": "长征五号B遥三",
                "header": "张三",
                "createTime": "2025-07-28",
                "company": "中国航天科技集团",
                "receiveTime": "2025-07-28T10:30:00",
                "status": "未开始",
                "batch": 1,
                "mqttName": "mqtt_connection_1"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1,
        "pages": 1
    }
}
```

### 1.4 根据ID获取航天器

**接口地址**: `GET /satellite/{id}`

**路径参数**:
- `id` (Long, 必填): 航天器ID

**请求示例**:
```
GET /satellite/1
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "model": "CZ-5B",
        "name": "长征五号B遥三",
        "header": "张三",
        "createTime": "2025-07-28",
        "company": "中国航天科技集团",
        "receiveTime": "2025-07-28T10:30:00",
        "status": "未开始",
        "batch": 1,
        "mqttName": "mqtt_connection_1"
    }
}
```

### 1.5 更新航天器

**接口地址**: `PUT /satellite/update`

**请求参数**:
```json
{
    "id": 1,
    "model": "CZ-5B",
    "name": "长征五号B遥四",
    "header": "李四",
    "company": "中国航天科技集团",
    "batch": 2,
    "mqttName": "mqtt_connection_2"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": 1,
        "model": "CZ-5B",
        "name": "长征五号B遥四",
        "header": "李四",
        "createTime": "2025-07-28",
        "company": "中国航天科技集团",
        "receiveTime": "2025-07-28T10:30:00",
        "status": "未开始",
        "batch": 2,
        "mqttName": "mqtt_connection_2"
    }
}
```

---

## 2. 批次管理

### 2.1 添加批次

**接口地址**: `POST /satellite/batch`

**请求参数**:
```json
[1, 2, 3]
```
或单个批次:
```json
1
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "inserted": 3
    }
}
```

### 2.2 查询所有批次

**接口地址**: `GET /satellite/batch`

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [1, 2, 3, 4, 5]
}
```

---

## 3. MQTT连接管理

### 3.1 MQTT连接测试

**接口地址**: `POST /satellite/mqtttest`

**请求参数**:
```json
{
    "mqtt_name": "mqtt_connection_1"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "connected": true,
        "message": "连接成功",
        "mqttName": "mqtt_connection_1"
    }
}
```

### 3.2 获取所有MQTT连接名称

**接口地址**: `GET /satellite/mqtttest`

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": ["mqtt_connection_1", "mqtt_connection_2", "mqtt_connection_3"]
}
```

---

## 4. 遥测代号文件上传

### 4.1 上传遥测代号文件（仅检查格式）

**接口地址**: `POST /satellite/upload/file`

**请求参数**:
- `file` (MultipartFile, 必填): Excel文件，支持.xlsx或.xls格式

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "success": 150,
        "failed": 5,
        "uploadId": "abc123def",
        "failFileUrl": "/download/fail_202507281030.xlsx"
    }
}
```

### 4.2 上传遥测代号文件（批次标识）

**接口地址**: `POST /satellite/upload/batch`

**请求参数**:
- `file` (MultipartFile, 必填): Excel文件
- `fileName` (String, 必填): 文件名（批次标识）

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "success": 150,
        "failed": 5,
        "fileName": "batch_001.xlsx",
        "failFileUrl": "/download/fail_202507281030.xlsx"
    }
}
```

### 4.3 导入遥测代号数据（绑定航天器）

**接口地址**: `POST /satellite/upload`

**请求参数**:
- `file` (MultipartFile, 必填): Excel文件
- `satelliteId` (Long, 必填): 航天器ID

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "success": 150,
        "failed": 5,
        "satelliteId": 1,
        "failFileUrl": "/download/fail_202507281030.xlsx"
    }
}
```

---

## 5. 遥测代号数据管理

### 5.1 查询航天器遥测代号

**接口地址**: `GET /satellite/telemetry`

**请求参数**:
- `id` (Long, 可选): 遥测代号ID
- `satelliteId` (Long, 可选): 航天器ID

**请求示例**:
```
GET /satellite/telemetry?satelliteId=1
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "serialNum": 1,
            "name": "TM001",
            "description": "温度传感器数据",
            "note": "主要用于监测设备温度",
            "spacecraftId": 1,
            "subsystemId": null,
            "singleId": null,
            "moduleId": null,
            "fileName": "batch_001.xlsx"
        }
    ]
}
```

### 5.2 删除遥测代号数据

**接口地址**: `DELETE /satellite/telemetry`

**请求参数**:
- `id` (String, 可选): 遥测代号ID列表，多个ID用英文逗号分隔
- `all` (Boolean, 可选): 是否删除所有，默认false

**请求示例**:
```
DELETE /satellite/telemetry?id=1,2,3
DELETE /satellite/telemetry?all=true
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "deleted": 3
    }
}
```

---

## 6. 批次遥测代号管理

### 6.1 根据文件名查询未绑定航天器的遥测代号数据

**接口地址**: `GET /satellite/telemetry/batch`

**请求参数**:
- `fileName` (String, 必填): 文件名（批次标识）

**请求示例**:
```
GET /satellite/telemetry/batch?fileName=batch_001.xlsx
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": [
        {
            "id": 1,
            "serialNum": 1,
            "name": "TM001",
            "description": "温度传感器数据",
            "note": "主要用于监测设备温度",
            "spacecraftId": null,
            "subsystemId": null,
            "singleId": null,
            "moduleId": null,
            "fileName": "batch_001.xlsx"
        }
    ]
}
```

### 6.2 根据文件名删除临时遥测代号数据

**接口地址**: `DELETE /satellite/telemetry/batch`

**请求参数**:
- `fileName` (String, 必填): 文件名（批次标识）

**请求示例**:
```
DELETE /satellite/telemetry/batch?fileName=batch_001.xlsx
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "deleted": 150,
        "message": "成功删除 150 条临时数据"
    }
}
```

### 6.3 将临时数据绑定到航天器

**接口地址**: `POST /satellite/telemetry/batch/bind`

**请求参数**:
- `satelliteId` (Long, 必填): 航天器ID
- `fileName` (String, 必填): 文件名（批次标识）

**请求示例**:
```
POST /satellite/telemetry/batch/bind?satelliteId=1&fileName=batch_001.xlsx
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "updated": 150,
        "satelliteId": 1,
        "fileName": "batch_001.xlsx",
        "message": "成功绑定 150 条遥测代号数据到航天器"
    }
}
```

---

## 7. 航天器绑定数据管理

### 7.1 获取航天器已绑定的文件信息

**接口地址**: `GET /satellite/bound-file/{satelliteId}`

**路径参数**:
- `satelliteId` (Long, 必填): 航天器ID

**请求示例**:
```
GET /satellite/bound-file/1
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "satelliteId": 1,
        "satelliteName": "长征五号B遥三",
        "boundFiles": [
            {
                "fileName": "batch_001.xlsx",
                "dataCount": 150,
                "bindTime": "2025-07-28T10:30:00"
            }
        ],
        "totalDataCount": 150
    }
}
```

### 7.2 删除航天器已绑定的遥测数据

**接口地址**: `DELETE /satellite/bound-data/{satelliteId}`

**路径参数**:
- `satelliteId` (Long, 必填): 航天器ID

**请求示例**:
```
DELETE /satellite/bound-data/1
```

**响应示例**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "deleted": 150,
        "satelliteId": 1,
        "satelliteName": "长征五号B遥三",
        "message": "成功删除航天器 长征五号B遥三 的 150 条遥测数据"
    }
}
```

---

## 8. 错误响应格式

当接口调用出现错误时，会返回以下格式的错误响应：

```json
{
    "code": 400,
    "message": "请求参数错误",
    "data": null
}
```

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 422 | 业务逻辑错误 |
| 500 | 服务器内部错误 |

---

## 9. 数据模型

### 9.1 航天器信息 (SatelliteDto)

```json
{
    "id": 1,
    "model": "CZ-5B",
    "name": "长征五号B遥三",
    "header": "张三",
    "createTime": "2025-07-28",
    "company": "中国航天科技集团",
    "receiveTime": "2025-07-28T10:30:00",
    "status": "未开始",
    "batch": 1,
    "mqttName": "mqtt_connection_1"
}
```

### 9.2 遥测代号信息 (TelemetryCodeDto)

```json
{
    "id": 1,
    "serialNum": 1,
    "name": "TM001",
    "description": "温度传感器数据",
    "note": "主要用于监测设备温度",
    "spacecraftId": 1,
    "subsystemId": null,
    "singleId": null,
    "moduleId": null,
    "fileName": "batch_001.xlsx"
}
```

### 9.3 分页结果 (PageResult)

```json
{
    "records": [],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
}
```

---

## 10. 注意事项

1. **文件上传限制**：
   - 仅支持 `.xlsx` 和 `.xls` 格式的Excel文件
   - 单次上传最多支持5000条数据
   - 文件大小建议不超过10MB

2. **批次号限制**：
   - 批次号必须在1-9999范围内
   - 批次号不能重复

3. **MQTT连接**：
   - MQTT名称不能为空
   - 连接测试会验证MQTT配置的有效性

4. **数据绑定流程**：
   - 先上传文件到临时存储（使用批次标识）
   - 预览数据确认无误后绑定到航天器
   - 绑定后数据从临时存储转移到正式存储

---

*文档生成时间：2025-07-28*
*API版本：v1.0*
```
