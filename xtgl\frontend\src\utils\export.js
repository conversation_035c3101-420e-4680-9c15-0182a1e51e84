import * as XLSX from 'xlsx'

/**
 * 导出表格数据为Excel文件
 * @param {Array} data - 要导出的数据数组
 * @param {Array} columns - 列配置数组，格式：[{key: 'field', title: '列标题', width: 120}]
 * @param {String} filename - 文件名（不包含扩展名）
 */
export function exportToExcel(data, columns, filename = 'export') {
  try {
    // 创建工作簿
    const workbook = XLSX.utils.book_new()
    
    // 准备表头
    const headers = columns.map(col => col.title || col.label || col.key)
    
    // 准备数据行
    const rows = data.map(item => {
      return columns.map(col => {
        const value = item[col.key] || item[col.prop] || ''
        // 处理特殊值
        if (value === null || value === undefined) {
          return ''
        }
        return value
      })
    })
    
    // 合并表头和数据
    const worksheetData = [headers, ...rows]
    
    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)
    
    // 设置列宽
    const colWidths = columns.map(col => ({
      wch: Math.max(col.width ? col.width / 8 : 15, (col.title || col.label || col.key).length + 2)
    }))
    worksheet['!cols'] = colWidths
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
    
    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const finalFilename = `${filename}_${timestamp}.xlsx`
    
    // 导出文件
    XLSX.writeFile(workbook, finalFilename)
    
    return true
  } catch (error) {
    console.error('导出Excel失败:', error)
    throw new Error('导出失败: ' + error.message)
  }
}

/**
 * 导出航天器列表数据
 * @param {Array} data - 航天器数据数组
 * @param {Object} searchParams - 搜索参数（用于文件名）
 */
export function exportSatelliteList(data, searchParams = {}) {
  const columns = [
    { key: 'model', title: '型号', width: 120 },
    { key: 'name', title: '名称', width: 150 },
    { key: 'header', title: '负责人', width: 100 },
    { key: 'createTime', title: '创建时间', width: 180 },
    { key: 'company', title: '所属单位', width: 150 },
    { key: 'formattedReceiveTime', title: '开始接收时间', width: 180 },
    { key: 'status', title: '卫星状态', width: 100 },
    { key: 'batch', title: '批次', width: 80 },
    { key: 'mqttName', title: 'MQTT名称', width: 120 }
  ]

  // 处理数据，格式化时间字段
  const processedData = data.map(item => {
    // 调试信息：检查原始数据
    console.log('导出数据项:', {
      name: item.name,
      status: item.status,
      receiveTime: item.receiveTime,
      originalItem: item
    })

    return {
      ...item,
      // 直接使用数据库中的状态值
      status: item.status || '未开始',
      // 格式化接收时间
      formattedReceiveTime: formatReceiveTimeForExport(item.receiveTime)
    }
  })

  // 生成文件名
  let filename = '航天器列表'
  if (searchParams.model) filename += `_型号${searchParams.model}`
  if (searchParams.name) filename += `_名称${searchParams.name}`
  if (searchParams.header) filename += `_负责人${searchParams.header}`
  if (searchParams.company) filename += `_单位${searchParams.company}`

  return exportToExcel(processedData, columns, filename)
}

/**
 * 格式化接收时间用于导出
 * @param {String|Date} receiveTime - 接收时间
 * @returns {String} 格式化后的时间字符串
 */
function formatReceiveTimeForExport(receiveTime) {
  if (!receiveTime) {
    return '未开始'
  }

  try {
    const date = new Date(receiveTime)
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return receiveTime.toString()
    }

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    console.warn('格式化时间失败:', error, '原始值:', receiveTime)
    return receiveTime ? receiveTime.toString() : '未开始'
  }
}

/**
 * 导出遥测代号数据
 * @param {Array} data - 遥测代号数据数组
 * @param {String} satelliteName - 航天器名称
 */
export function exportTelemetryData(data, satelliteName = '航天器') {
  const columns = [
    { key: 'serialNum', title: '序号', width: 80 },
    { key: 'code', title: '遥测代号', width: 120 },
    { key: 'description', title: '代号描述', width: 200 },
    { key: 'unit', title: '单位', width: 80 },
    { key: 'remark', title: '备注', width: 150 }
  ]
  
  const filename = `${satelliteName}_遥测代号`
  
  return exportToExcel(data, columns, filename)
}
