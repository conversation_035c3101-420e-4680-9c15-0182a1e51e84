# 测试数据模板说明

## Excel文件格式要求

### 文件名
- 建议命名：`test_telemetry.xlsx`
- 支持格式：`.xlsx` 或 `.xls`

### 表格结构

#### 必需列（必须包含）
| 列名 | 数据类型 | 说明 | 示例 |
|------|----------|------|------|
| 序号 | 整数 | 遥测代号的序号 | 1, 2, 3... |
| 代号名称 | 字符串 | 遥测代号的名称 | TM001, TM002 |

#### 可选列（建议包含）
| 列名 | 数据类型 | 说明 | 示例 |
|------|----------|------|------|
| 代号描述 | 字符串 | 遥测代号的详细描述 | 温度传感器数据 |
| 备注 | 字符串 | 额外的备注信息 | 主要监测设备温度 |
| 分系统ID | 整数 | 所属分系统的ID | 1, 2, 3 |
| 单机ID | 整数 | 所属单机的ID | 1, 2, 3 |
| 模块ID | 整数 | 所属模块的ID | 1, 2, 3 |

## 测试数据模板

### 基础测试数据
```
序号 | 代号名称 | 代号描述 | 备注
-----|----------|----------|------
1    | TM001    | 温度传感器数据 | 主要监测设备温度
2    | TM002    | 压力传感器数据 | 监测系统压力
3    | TM003    | 电压传感器数据 | 监测电源电压
4    | TM004    | 湿度传感器数据 | 监测环境湿度
5    | TM005    | 速度传感器数据 | 监测运行速度
```

### 扩展测试数据（包含更多字段）
```
序号 | 代号名称 | 代号描述 | 备注 | 分系统ID | 单机ID | 模块ID
-----|----------|----------|------|----------|--------|--------
1    | TM001    | 温度传感器数据 | 主要监测设备温度 | 1 | 1 | 1
2    | TM002    | 压力传感器数据 | 监测系统压力 | 1 | 1 | 2
3    | TM003    | 电压传感器数据 | 监测电源电压 | 2 | 2 | 1
4    | TM004    | 湿度传感器数据 | 监测环境湿度 | 2 | 2 | 2
5    | TM005    | 速度传感器数据 | 监测运行速度 | 3 | 3 | 1
6    | TM006    | 加速度传感器数据 | 监测加速度变化 | 3 | 3 | 2
7    | TM007    | 位置传感器数据 | 监测位置信息 | 1 | 2 | 3
8    | TM008    | 状态指示器数据 | 设备状态监测 | 2 | 1 | 3
9    | TM009    | 电流传感器数据 | 监测电流变化 | 3 | 2 | 1
10   | TM010    | 功率传感器数据 | 监测功率消耗 | 1 | 3 | 2
```

## 创建Excel文件步骤

### 使用Microsoft Excel
1. 打开Microsoft Excel
2. 在第一行输入列标题
3. 从第二行开始输入测试数据
4. 保存为 `.xlsx` 格式

### 使用Google Sheets
1. 打开Google Sheets
2. 输入表头和数据
3. 下载为Excel格式（.xlsx）

### 使用LibreOffice Calc
1. 打开LibreOffice Calc
2. 输入数据
3. 另存为Excel格式

## 数据验证规则

### 必填字段验证
- **序号**：必须为正整数，不能为空
- **代号名称**：不能为空，建议使用有意义的命名

### 数据格式要求
- **序号**：1-9999之间的整数
- **代号名称**：1-64个字符
- **代号描述**：最多255个字符
- **备注**：最多255个字符
- **ID字段**：正整数或空值

### 常见错误示例
❌ **错误数据**：
```
序号 | 代号名称 | 代号描述
-----|----------|----------
     | TM001    | 温度传感器  # 序号为空
1    |          | 压力传感器  # 代号名称为空
abc  | TM003    | 电压传感器  # 序号不是数字
```

✅ **正确数据**：
```
序号 | 代号名称 | 代号描述
-----|----------|----------
1    | TM001    | 温度传感器
2    | TM002    | 压力传感器
3    | TM003    | 电压传感器
```

## 测试场景数据

### 场景1：正常数据测试
- 5-10条标准格式数据
- 包含所有必需字段
- 用于验证基本功能

### 场景2：大数据量测试
- 100-1000条数据
- 测试批量处理性能
- 验证系统稳定性

### 场景3：边界值测试
- 最大长度的字符串
- 最大值的数字
- 测试系统边界处理

### 场景4：错误数据测试
- 包含空值的必填字段
- 格式错误的数据
- 测试错误处理机制

## 文件大小限制

- **推荐大小**：小于5MB
- **最大行数**：建议不超过5000行
- **最大列数**：支持所有定义的列

## 注意事项

1. **编码格式**：确保使用UTF-8编码，支持中文字符
2. **日期格式**：如果包含日期，使用标准格式（YYYY-MM-DD）
3. **数字格式**：避免使用千分位分隔符
4. **空值处理**：可选字段可以留空，但不要使用"NULL"字符串
5. **特殊字符**：避免使用特殊符号，如：`<>?*|"`

## 快速生成测试数据

### Python脚本示例
```python
import pandas as pd

# 生成测试数据
data = []
for i in range(1, 11):
    data.append({
        '序号': i,
        '代号名称': f'TM{i:03d}',
        '代号描述': f'传感器数据{i}',
        '备注': f'测试数据{i}'
    })

# 创建DataFrame并保存
df = pd.DataFrame(data)
df.to_excel('test_telemetry.xlsx', index=False)
```

### 在线工具
- 可以使用在线Excel生成器
- 或者使用数据生成网站
- 确保数据格式符合要求

## 验证测试数据

上传前建议验证：
1. 文件能正常打开
2. 列标题正确
3. 数据格式符合要求
4. 没有明显的错误数据

通过准备好的测试数据，可以确保Postman测试的顺利进行和功能的全面验证。
