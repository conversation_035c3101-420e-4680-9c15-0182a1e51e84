package com.xtgl.ssystem.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xtgl.ssystem.common.entity.Satellite;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 航天器Mapper接口
 */
@Mapper
public interface SatelliteMapper extends BaseMapper<Satellite> {

    /**
     * 分页条件查询航天器
     *
     * @param page    分页参数
     * @param model   型号
     * @param name    名称（模糊）
     * @param header  负责人（模糊）
     * @param company 所属单位（模糊）
     * @return 分页结果
     */
    IPage<Satellite> selectPageByCondition(
            Page<Satellite> page,
            @Param("model") String model,
            @Param("name") String name,
            @Param("header") String header,
            @Param("company") String company);
            
    /**
     * 批量插入批次
     * 
     * @param batches 批次列表
     * @return 插入数量
     */
    int batchInsertBatches(List<Integer> batches);
    
    /**
     * 查询所有批次（按降序排列）
     * 
     * @return 批次列表
     */
    List<Integer> selectAllBatches();
    
    /**
     * 根据MQTT名称查找相关联的航天器数量
     * 
     * @param mqttName MQTT名称
     * @return 关联的航天器数量
     */
    int countByMqttName(@Param("mqttName") String mqttName);
} 