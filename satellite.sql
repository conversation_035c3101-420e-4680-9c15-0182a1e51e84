CREATE TABLE `satellite` (
    `id`                BIGINT AUTO_INCREMENT COMMENT '主键',
    `model`             VARCHAR(64)  NOT NULL COMMENT '型号',
    `name`              VARCHAR(64)  NOT NULL COMMENT '名称',
    `header`            VARCHAR(48)  NOT NULL COMMENT '负责人',
    `create_time`       DATE         NOT NULL COMMENT '创建时间',
    `company`           VARCHAR(100) NOT NULL COMMENT '所属单位',
    `receive_time`      DATETIME     DEFAULT NULL COMMENT '开始接收时间',
    `status`            VARCHAR(24)  NOT NULL COMMENT '卫星状态（4种）',
    `batch`             INT          NOT NULL COMMENT '批次',
    `mqtt_name`         VARCHAR(64)  DEFAULT NULL COMMENT 'MQTT名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='航天器信息表';