<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xtgl.ssystem.mapper.AlgorithmMapper">

    <!-- 分页搜索算法 -->
    <select id="searchAlgorithms" resultType="com.xtgl.ssystem.common.entity.Algorithm">
        SELECT * FROM algorithm
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>
