# 算法管理接口文档

## 接口概述

算法管理模块提供了算法的搜索、查看和状态更新功能。支持按算法类型分类浏览和按名称搜索。

## 1. 搜索算法

### 接口信息
- **路径**: `/algorithm/search`
- **方法**: `GET`
- **功能**: 根据算法名称关键字模糊搜索，并支持通过算法类型枚举值进行过滤

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 算法名称关键字，支持模糊匹配 |
| type | string | 否 | 算法类型枚举：UNSUPERVISED、SUPERVISED、DEEP_LEARNING |
| pageNum | int | 否 | 页码，默认 1，必须大于0 |
| pageSize | int | 否 | 每页条数，默认 10，范围1-100 |

### 算法类型说明

| 枚举值 | 中文名称 | 数据库值 |
|--------|----------|----------|
| UNSUPERVISED | 无监督算法 | 1 |
| SUPERVISED | 监督算法 | 2 |
| DEEP_LEARNING | 深度学习算法 | 3 |

### 示例请求
```
GET /algorithm/search?name=Random&type=SUPERVISED&pageNum=1&pageSize=5
```

### 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "records": [
            {
                "id": 1,
                "name": "随机森林",
                "direction": "分类回归",
                "description": "随机森林是一种集成学习方法，结合多个决策树进行预测",
                "type": 2,
                "typeName": "监督算法",
                "enabled": true,
                "enabledText": "启用"
            }
        ],
        "total": 1,
        "size": 5,
        "current": 1,
        "pages": 1
    }
}
```

## 2. 获取算法详情

### 接口信息
- **路径**: `/algorithm/{id}`
- **方法**: `GET`
- **功能**: 根据算法ID获取详细信息

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 算法ID |

### 示例请求
```
GET /algorithm/1
```

### 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "name": "随机森林",
        "direction": "分类回归",
        "description": "随机森林是一种集成学习方法，结合多个决策树进行预测",
        "type": 2,
        "typeName": "监督算法",
        "enabled": true,
        "enabledText": "启用"
    }
}
```

## 3. 更新算法状态

### 接口信息
- **路径**: `/algorithm/update`
- **方法**: `PUT`
- **功能**: 只能更新算法的启停状态，其他信息不可修改

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 算法ID，必须大于0 |
| enabled | boolean | 是 | 启用状态，true-启用，false-停用 |

### 示例请求
```
PUT /algorithm/update?id=1&enabled=false
```

### 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 404 | 算法不存在 |
| 500 | 服务器内部错误 |

## 前端页面功能

### 页面布局
1. **页面标题**: "算法管理"
2. **算法类型按钮组**: 三个连在一起的按钮
   - 无监督算法
   - 监督算法  
   - 深度学习算法
3. **搜索区域**: 算法名称输入框 + 搜索/重置按钮
4. **数据表格**: 显示算法信息
5. **分页组件**: 支持分页浏览

### 交互功能
- **类型切换**: 点击不同类型按钮切换数据
- **搜索**: 在当前类型范围内按名称搜索
- **编辑**: 点击编辑按钮修改启用状态
- **分页**: 支持每页10/20/50/100条记录

### 表格列说明
- **算法名称**: 显示算法的名称
- **擅长方向**: 显示算法的应用领域
- **介绍**: 显示算法的详细描述
- **启停状态**: 用标签显示启用/停用状态
- **操作**: 只有编辑按钮

### 编辑对话框
- 算法名称、擅长方向、介绍字段为只读
- 只能修改启用状态（启用/停用单选按钮）
- 支持取消和确定操作

## 数据库表结构

```sql
CREATE TABLE `algorithm` (
    `id`            BIGINT AUTO_INCREMENT COMMENT '主键',
    `name`          VARCHAR(48)  NOT NULL COMMENT '算法名称',
    `direction`     VARCHAR(32)  NOT NULL COMMENT '擅长方向',
    `description`   VARCHAR(255) DEFAULT NULL COMMENT '介绍',
    `type`          TINYINT      NOT NULL COMMENT '算法类型：1-无监督算法 2-监督算法 3-深度学习算法',
    `enabled`       BOOLEAN      DEFAULT TRUE COMMENT '启用状态',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='算法表';
```
